# Prometheus配置文件 - 生产环境
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'fitness-ai-production'
    environment: 'production'

# 告警规则文件
rule_files:
  - "rules/*.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 数据采集配置
scrape_configs:
  # AI助手应用指标
  - job_name: 'ai-assistant'
    static_configs:
      - targets: ['ai-assistant:8000']
    metrics_path: /metrics
    scrape_interval: 5s
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # PostgreSQL数据库指标
  - job_name: 'postgresql'
    static_configs:
      - targets: ['postgres:5432']
    metrics_path: /metrics
    scrape_interval: 30s

  # Redis缓存指标
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: /metrics
    scrape_interval: 15s

  # Nginx负载均衡器指标
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    metrics_path: /nginx_status
    scrape_interval: 15s

  # 系统指标 (Node Exporter)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # Prometheus自身指标
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s

  # Grafana指标
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    metrics_path: /metrics
    scrape_interval: 30s

  # AlertManager指标
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager:9093']
    metrics_path: /metrics
    scrape_interval: 30s

  # 自定义业务指标
  - job_name: 'ai-assistant-business'
    static_configs:
      - targets: ['ai-assistant:8000']
    metrics_path: /business-metrics
    scrape_interval: 10s
    params:
      module: ['business']

# 远程写入配置 (可选 - 用于长期存储)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint/api/v1/write"
#     basic_auth:
#       username: "username"
#       password: "password"

# 远程读取配置 (可选)
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint/api/v1/read"
#     basic_auth:
#       username: "username"
#       password: "password"
