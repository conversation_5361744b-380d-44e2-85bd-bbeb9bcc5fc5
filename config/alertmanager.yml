# AlertManager配置文件 - 生产环境
global:
  # SMTP配置
  smtp_smarthost: '${SMTP_HOST}:${SMTP_PORT}'
  smtp_from: '${ALERT_EMAIL_FROM}'
  smtp_auth_username: '${SMTP_USERNAME}'
  smtp_auth_password: '${SMTP_PASSWORD}'
  smtp_require_tls: true

# 告警路由配置
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  routes:
    # 严重告警立即通知
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m
    
    # 警告级别告警
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 30s
      repeat_interval: 30m
    
    # 业务告警
    - match:
        type: business
      receiver: 'business-alerts'
      group_wait: 1m
      repeat_interval: 1h
    
    # 数据库告警
    - match:
        service: postgresql
      receiver: 'database-alerts'
      group_wait: 30s
      repeat_interval: 15m
    
    # Redis告警
    - match:
        service: redis
      receiver: 'cache-alerts'
      group_wait: 30s
      repeat_interval: 15m

# 告警接收器配置
receivers:
  # 默认接收器
  - name: 'default-receiver'
    email_configs:
      - to: '${DEFAULT_ALERT_EMAIL}'
        subject: '[AI健身助手] {{ .GroupLabels.alertname }} - {{ .Status | toUpper }}'
        body: |
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警描述: {{ .Annotations.description }}
          告警级别: {{ .Labels.severity }}
          服务名称: {{ .Labels.service }}
          开始时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ if .EndsAt }}结束时间: {{ .EndsAt.Format "2006-01-02 15:04:05" }}{{ end }}
          
          标签信息:
          {{ range .Labels.SortedPairs }}  {{ .Name }}: {{ .Value }}
          {{ end }}
          {{ end }}

  # 严重告警接收器
  - name: 'critical-alerts'
    email_configs:
      - to: '${CRITICAL_ALERT_EMAIL}'
        subject: '🚨 [严重告警] {{ .GroupLabels.alertname }}'
        body: |
          ⚠️ 检测到严重告警，请立即处理！
          
          {{ range .Alerts }}
          📋 告警信息:
          • 名称: {{ .Annotations.summary }}
          • 描述: {{ .Annotations.description }}
          • 服务: {{ .Labels.service }}
          • 级别: {{ .Labels.severity }}
          • 时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          
          🔍 详细标签:
          {{ range .Labels.SortedPairs }}  • {{ .Name }}: {{ .Value }}
          {{ end }}
          {{ end }}
          
          请访问监控面板查看详细信息: ${GRAFANA_URL}
    
    # 钉钉通知 (可选)
    webhook_configs:
      - url: '${DINGTALK_WEBHOOK_URL}'
        send_resolved: true
        title: '🚨 AI健身助手严重告警'
        text: |
          {{ range .Alerts }}
          **告警**: {{ .Annotations.summary }}
          **描述**: {{ .Annotations.description }}
          **服务**: {{ .Labels.service }}
          **时间**: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

  # 警告级别告警接收器
  - name: 'warning-alerts'
    email_configs:
      - to: '${WARNING_ALERT_EMAIL}'
        subject: '⚠️ [警告] {{ .GroupLabels.alertname }}'
        body: |
          检测到警告级别告警，请关注处理。
          
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警描述: {{ .Annotations.description }}
          服务名称: {{ .Labels.service }}
          开始时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

  # 业务告警接收器
  - name: 'business-alerts'
    email_configs:
      - to: '${BUSINESS_ALERT_EMAIL}'
        subject: '📊 [业务告警] {{ .GroupLabels.alertname }}'
        body: |
          检测到业务指标异常，请业务团队关注。
          
          {{ range .Alerts }}
          业务指标: {{ .Annotations.summary }}
          异常描述: {{ .Annotations.description }}
          当前状态: {{ .Labels.severity }}
          检测时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
          
          建议检查:
          1. 用户行为是否异常
          2. 系统功能是否正常
          3. 数据处理是否有误

  # 数据库告警接收器
  - name: 'database-alerts'
    email_configs:
      - to: '${DBA_ALERT_EMAIL}'
        subject: '🗄️ [数据库告警] {{ .GroupLabels.alertname }}'
        body: |
          数据库系统检测到异常，请DBA团队处理。
          
          {{ range .Alerts }}
          数据库告警: {{ .Annotations.summary }}
          问题描述: {{ .Annotations.description }}
          数据库实例: {{ .Labels.instance }}
          告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

  # 缓存告警接收器
  - name: 'cache-alerts'
    email_configs:
      - to: '${CACHE_ALERT_EMAIL}'
        subject: '💾 [缓存告警] {{ .GroupLabels.alertname }}'
        body: |
          Redis缓存系统检测到异常。
          
          {{ range .Alerts }}
          缓存告警: {{ .Annotations.summary }}
          问题描述: {{ .Annotations.description }}
          缓存实例: {{ .Labels.instance }}
          告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

# 告警抑制规则
inhibit_rules:
  # 如果服务完全不可用，抑制其他相关告警
  - source_match:
      alertname: ServiceDown
    target_match:
      service: ai-assistant
    equal: ['service', 'instance']
  
  # 如果数据库不可用，抑制数据库相关的其他告警
  - source_match:
      alertname: DatabaseDown
    target_match:
      service: postgresql
    equal: ['service', 'instance']
  
  # 如果Redis不可用，抑制Redis相关的其他告警
  - source_match:
      alertname: RedisDown
    target_match:
      service: redis
    equal: ['service', 'instance']

# 静默规则模板 (可通过API动态配置)
templates:
  - '/etc/alertmanager/templates/*.tmpl'
