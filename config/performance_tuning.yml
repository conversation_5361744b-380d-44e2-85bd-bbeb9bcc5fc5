# 性能调优配置文件
# 用于生产环境的性能优化设置

# 应用层性能配置
application:
  # FastAPI/Uvicorn配置
  uvicorn:
    workers: 4
    worker_class: "uvicorn.workers.UvicornWorker"
    max_requests: 1000
    max_requests_jitter: 100
    timeout: 30
    keepalive: 5
    backlog: 2048
    
  # 异步配置
  async_settings:
    max_connections: 100
    connection_pool_size: 20
    connection_timeout: 30
    read_timeout: 30
    write_timeout: 30
    
  # 缓存配置
  cache:
    default_ttl: 3600  # 1小时
    max_memory_usage: "2GB"
    eviction_policy: "lru"
    compression: true
    
  # 数据库连接池配置
  database:
    pool_size: 20
    max_overflow: 30
    pool_timeout: 30
    pool_recycle: 3600
    pool_pre_ping: true
    
  # Redis配置
  redis:
    max_connections: 50
    connection_timeout: 5
    socket_timeout: 5
    retry_on_timeout: true
    health_check_interval: 30

# 系统级性能配置
system:
  # 内存配置
  memory:
    heap_size: "3GB"
    gc_threshold: 0.8
    memory_limit: "4GB"
    swap_usage: "minimal"
    
  # CPU配置
  cpu:
    max_cpu_usage: 80
    cpu_affinity: "auto"
    thread_pool_size: 8
    
  # 网络配置
  network:
    tcp_keepalive: true
    tcp_nodelay: true
    socket_buffer_size: 65536
    max_connections: 1000
    
  # 文件系统配置
  filesystem:
    max_open_files: 65536
    io_scheduler: "deadline"
    read_ahead: 256

# 数据库性能优化
database_tuning:
  postgresql:
    # 连接配置
    max_connections: 200
    shared_buffers: "1GB"
    effective_cache_size: "3GB"
    
    # 内存配置
    work_mem: "16MB"
    maintenance_work_mem: "256MB"
    
    # 检查点配置
    checkpoint_completion_target: 0.9
    wal_buffers: "16MB"
    
    # 查询优化
    random_page_cost: 1.1
    effective_io_concurrency: 200
    
    # 日志配置
    log_min_duration_statement: 1000
    log_checkpoints: true
    log_connections: true
    log_disconnections: true
    
    # 自动清理配置
    autovacuum: true
    autovacuum_max_workers: 3
    autovacuum_naptime: "1min"

# Redis性能优化
redis_tuning:
  # 内存配置
  maxmemory: "2GB"
  maxmemory_policy: "allkeys-lru"
  
  # 持久化配置
  save_intervals:
    - "900 1"    # 900秒内至少1个key变化
    - "300 10"   # 300秒内至少10个key变化
    - "60 10000" # 60秒内至少10000个key变化
  
  # 网络配置
  tcp_keepalive: 300
  timeout: 0
  tcp_backlog: 511
  
  # 客户端配置
  maxclients: 10000
  
  # 性能配置
  hash_max_ziplist_entries: 512
  hash_max_ziplist_value: 64
  list_max_ziplist_size: -2
  set_max_intset_entries: 512
  zset_max_ziplist_entries: 128
  zset_max_ziplist_value: 64

# 负载均衡配置
load_balancer:
  nginx:
    # 工作进程配置
    worker_processes: "auto"
    worker_connections: 1024
    
    # 缓冲区配置
    client_body_buffer_size: "128k"
    client_max_body_size: "10m"
    client_header_buffer_size: "1k"
    large_client_header_buffers: "4 4k"
    
    # 超时配置
    client_body_timeout: 12
    client_header_timeout: 12
    keepalive_timeout: 15
    send_timeout: 10
    
    # 压缩配置
    gzip: true
    gzip_vary: true
    gzip_min_length: 10240
    gzip_proxied: "expired no-cache no-store private must-revalidate"
    gzip_types:
      - "text/plain"
      - "text/css"
      - "text/xml"
      - "text/javascript"
      - "application/json"
      - "application/javascript"
      - "application/xml+rss"
      - "application/atom+xml"
    
    # 缓存配置
    proxy_cache_valid: "200 302 10m"
    proxy_cache_valid_404: "1m"
    proxy_cache_use_stale: "error timeout invalid_header updating"

# 监控性能配置
monitoring:
  # 指标收集频率
  metrics_interval: 15  # 秒
  
  # 性能阈值
  thresholds:
    response_time:
      warning: 100   # ms
      critical: 200  # ms
    
    error_rate:
      warning: 0.01  # 1%
      critical: 0.05 # 5%
    
    cpu_usage:
      warning: 70    # %
      critical: 85   # %
    
    memory_usage:
      warning: 80    # %
      critical: 90   # %
    
    disk_usage:
      warning: 80    # %
      critical: 90   # %
    
    database_connections:
      warning: 150
      critical: 180
    
    cache_hit_rate:
      warning: 0.7   # 70%
      critical: 0.5  # 50%

# 自动扩缩容配置
autoscaling:
  # 水平扩缩容
  horizontal:
    min_replicas: 3
    max_replicas: 10
    target_cpu_utilization: 70
    target_memory_utilization: 80
    scale_up_stabilization: 60    # 秒
    scale_down_stabilization: 300 # 秒
    
  # 垂直扩缩容
  vertical:
    enabled: false
    min_cpu: "500m"
    max_cpu: "4000m"
    min_memory: "1Gi"
    max_memory: "8Gi"

# 性能测试配置
performance_testing:
  # 负载测试参数
  load_test:
    concurrent_users: 100
    test_duration: 300  # 秒
    ramp_up_time: 60   # 秒
    think_time: 5      # 秒
    
  # 压力测试参数
  stress_test:
    concurrent_users: 500
    test_duration: 600  # 秒
    ramp_up_time: 120  # 秒
    
  # 容量测试参数
  capacity_test:
    max_users: 1000
    step_size: 50
    step_duration: 120  # 秒
    
  # 性能基准
  benchmarks:
    target_rps: 1000
    target_response_time: 50  # ms
    target_error_rate: 0.001  # 0.1%
    target_availability: 0.9999  # 99.99%

# 优化建议
optimization_recommendations:
  # 代码级优化
  code_level:
    - "使用异步编程模式"
    - "实现连接池复用"
    - "优化数据库查询"
    - "使用缓存减少重复计算"
    - "实现批量处理"
    
  # 架构级优化
  architecture_level:
    - "实现微服务架构"
    - "使用CDN加速静态资源"
    - "实现读写分离"
    - "使用消息队列异步处理"
    - "实现分布式缓存"
    
  # 基础设施优化
  infrastructure_level:
    - "使用SSD存储"
    - "优化网络配置"
    - "实现负载均衡"
    - "使用容器编排"
    - "实现自动扩缩容"
