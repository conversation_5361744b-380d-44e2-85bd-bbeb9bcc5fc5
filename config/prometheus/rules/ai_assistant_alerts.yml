# AI健身助手告警规则
groups:
  - name: ai_assistant_alerts
    rules:
      # 高响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="ai-assistant"}[5m])) > 0.1
        for: 2m
        labels:
          severity: warning
          service: ai-assistant
        annotations:
          summary: "AI助手响应时间过高"
          description: "AI助手95%响应时间超过100ms，当前值: {{ $value }}s"

      # 错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{job="ai-assistant",status=~"5.."}[5m]) / rate(http_requests_total{job="ai-assistant"}[5m]) > 0.01
        for: 1m
        labels:
          severity: critical
          service: ai-assistant
        annotations:
          summary: "AI助手错误率过高"
          description: "AI助手错误率超过1%，当前值: {{ $value | humanizePercentage }}"

      # 服务不可用告警
      - alert: ServiceDown
        expr: up{job="ai-assistant"} == 0
        for: 30s
        labels:
          severity: critical
          service: ai-assistant
        annotations:
          summary: "AI助手服务不可用"
          description: "AI助手服务已停止响应超过30秒"

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: (process_resident_memory_bytes{job="ai-assistant"} / 1024 / 1024 / 1024) > 3
        for: 5m
        labels:
          severity: warning
          service: ai-assistant
        annotations:
          summary: "AI助手内存使用率过高"
          description: "AI助手内存使用超过3GB，当前值: {{ $value }}GB"

      # CPU使用率告警
      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total{job="ai-assistant"}[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: ai-assistant
        annotations:
          summary: "AI助手CPU使用率过高"
          description: "AI助手CPU使用率超过80%，当前值: {{ $value }}%"

      # 缓存命中率低告警
      - alert: LowCacheHitRate
        expr: cache_hit_rate{job="ai-assistant"} < 0.7
        for: 10m
        labels:
          severity: warning
          service: ai-assistant
        annotations:
          summary: "缓存命中率过低"
          description: "缓存命中率低于70%，当前值: {{ $value | humanizePercentage }}"

  - name: database_alerts
    rules:
      # 数据库连接数告警
      - alert: HighDatabaseConnections
        expr: pg_stat_database_numbackends{job="postgresql"} > 80
        for: 5m
        labels:
          severity: warning
          service: postgresql
        annotations:
          summary: "数据库连接数过高"
          description: "PostgreSQL连接数超过80，当前值: {{ $value }}"

      # 数据库不可用告警
      - alert: DatabaseDown
        expr: up{job="postgresql"} == 0
        for: 30s
        labels:
          severity: critical
          service: postgresql
        annotations:
          summary: "数据库服务不可用"
          description: "PostgreSQL数据库已停止响应超过30秒"

      # 数据库查询时间告警
      - alert: SlowDatabaseQueries
        expr: pg_stat_database_blk_read_time{job="postgresql"} / pg_stat_database_blks_read{job="postgresql"} > 10
        for: 5m
        labels:
          severity: warning
          service: postgresql
        annotations:
          summary: "数据库查询时间过长"
          description: "数据库平均查询时间超过10ms"

  - name: redis_alerts
    rules:
      # Redis内存使用告警
      - alert: HighRedisMemoryUsage
        expr: redis_memory_used_bytes{job="redis"} / redis_memory_max_bytes{job="redis"} > 0.8
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis内存使用率超过80%，当前值: {{ $value | humanizePercentage }}"

      # Redis不可用告警
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 30s
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis服务不可用"
          description: "Redis缓存服务已停止响应超过30秒"

      # Redis连接数告警
      - alert: HighRedisConnections
        expr: redis_connected_clients{job="redis"} > 100
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis连接数过高"
          description: "Redis连接数超过100，当前值: {{ $value }}"

  - name: system_alerts
    rules:
      # 磁盘空间告警
      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_free_bytes{fstype!="tmpfs"}) / node_filesystem_size_bytes{fstype!="tmpfs"} > 0.8
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "磁盘空间使用率过高"
          description: "磁盘使用率超过80%，当前值: {{ $value | humanizePercentage }}"

      # 系统负载告警
      - alert: HighSystemLoad
        expr: node_load1 > 4
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "系统负载过高"
          description: "系统1分钟负载超过4，当前值: {{ $value }}"

  - name: business_alerts
    rules:
      # 用户注册失败率告警
      - alert: HighUserRegistrationFailureRate
        expr: rate(user_registration_failures_total[5m]) / rate(user_registration_attempts_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: ai-assistant
          type: business
        annotations:
          summary: "用户注册失败率过高"
          description: "用户注册失败率超过10%，当前值: {{ $value | humanizePercentage }}"

      # AI对话失败率告警
      - alert: HighAIConversationFailureRate
        expr: rate(ai_conversation_failures_total[5m]) / rate(ai_conversation_attempts_total[5m]) > 0.05
        for: 3m
        labels:
          severity: critical
          service: ai-assistant
          type: business
        annotations:
          summary: "AI对话失败率过高"
          description: "AI对话失败率超过5%，当前值: {{ $value | humanizePercentage }}"

      # 训练计划生成失败率告警
      - alert: HighTrainingPlanFailureRate
        expr: rate(training_plan_generation_failures_total[5m]) / rate(training_plan_generation_attempts_total[5m]) > 0.02
        for: 5m
        labels:
          severity: warning
          service: ai-assistant
          type: business
        annotations:
          summary: "训练计划生成失败率过高"
          description: "训练计划生成失败率超过2%，当前值: {{ $value | humanizePercentage }}"
