#!/usr/bin/env python3
"""
测试创建训练模板功能是否恢复正常
"""

import sys
import os
sys.path.insert(0, '/home/<USER>/backend')

from app.services.training_template_service import TrainingTemplateService
from app.schemas.training_plan import TrainingTemplateCreate, TrainingTemplateExerciseCreate, SetRecordCreate
from app.db.session import get_db
from app.models.user import User

def test_template_creation():
    """测试创建训练模板功能"""
    
    print("🧪 测试创建训练模板功能")
    
    db = next(get_db())
    try:
        # 获取测试用户
        user = db.query(User).first()
        if not user:
            print("❌ 没有找到测试用户")
            return False
        
        print(f"✅ 找到测试用户: {user.id}")
        
        # 创建训练模板服务
        template_service = TrainingTemplateService(db)
        
        # 创建测试数据（包含 visibility 字段）
        template_data = TrainingTemplateCreate(
            name="测试模板 - 修复后",
            description="测试 visibility 字段修复",
            estimated_duration=60,
            target_body_parts=[2, 6],  # 胸部、肩部
            training_scenario="gym",
            visibility="private",  # 关键字段
            notes="测试备注",
            exercises=[
                TrainingTemplateExerciseCreate(
                    exercise_id=1,
                    sets=3,
                    reps="10",
                    weight="20",
                    rest_seconds=60,
                    exercise_type="weight_reps",
                    set_records=[
                        SetRecordCreate(
                            id="test_set_1",
                            set_number=1,
                            set_type="normal",
                            weight=20.0,
                            reps=10,
                            completed=False
                        ),
                        SetRecordCreate(
                            id="test_set_2",
                            set_number=2,
                            set_type="normal",
                            weight=20.0,
                            reps=10,
                            completed=False
                        )
                    ]
                )
            ]
        )
        
        print(f"📋 创建模板数据，visibility: {template_data.visibility}")
        
        # 尝试创建模板
        created_template = template_service.create_template(template_data, user.id)
        
        if created_template:
            template_dict = created_template.to_dict()
            print(f"✅ 模板创建成功！")
            print(f"  • 模板ID: {template_dict.get('id')}")
            print(f"  • 模板名称: {template_dict.get('name')}")
            print(f"  • visibility: {template_dict.get('visibility')}")
            print(f"  • 运动数量: {template_dict.get('exercise_count')}")
            
            # 检查组记录
            exercises = template_dict.get('exercises', [])
            if exercises:
                first_exercise = exercises[0]
                set_records = first_exercise.get('set_records', [])
                print(f"  • 组记录数量: {len(set_records)}")
                if set_records:
                    print(f"  • 第一个组记录ID: {set_records[0].get('id')} (类型: {type(set_records[0].get('id'))})")
            
            print("✅ 创建训练模板功能已恢复正常！")
            return True
        else:
            print("❌ 模板创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = test_template_creation()
    if success:
        print("\n🎉 测试通过！PostgreSQL 错误已解决！")
    else:
        print("\n❌ 测试失败")
        sys.exit(1)
