#!/usr/bin/env python3
"""
测试训练模板更新接口的修复
验证visibility字段和组记录数据的正确处理
"""

import sys
import os
sys.path.insert(0, '/home/<USER>/backend')

import json
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from app.main import app
from app.db.session import get_db
from app.models.exercise import Exercise
from app.models.training_template import WorkoutTemplate
from app.models.workout_exercise import WorkoutExercise
from app.models.set_record import SetRecord
from app.models.user import User

def test_visibility_field_support():
    """测试visibility字段的支持"""
    
    print("🧪 测试visibility字段支持")
    
    # 获取数据库会话
    db: Session = next(get_db())
    
    try:
        from app.services.training_template_service import TrainingTemplateService
        from app.schemas.training_plan import TrainingTemplateCreate, TrainingTemplateExerciseCreate
        
        # 创建测试数据
        template_service = TrainingTemplateService(db)
        
        # 模拟前端传输的数据（包含visibility字段）
        template_data = TrainingTemplateCreate(
            name="测试模板",
            description="测试描述",
            estimated_duration=60,
            target_body_parts=[2, 6],  # 胸部、肩部
            training_scenario="gym",
            visibility="private",  # 关键字段
            notes="测试备注",
            exercises=[
                TrainingTemplateExerciseCreate(
                    exercise_id=1,
                    sets=3,
                    reps="10",
                    weight="20",
                    rest_seconds=60,
                    exercise_type="weight_reps"
                )
            ]
        )
        
        print(f"✅ 创建TrainingTemplateCreate对象成功，visibility: {template_data.visibility}")
        
        # 测试创建模板
        user = db.query(User).first()
        if not user:
            print("❌ 没有找到测试用户")
            return False
        
        created_template = template_service.create_template(template_data, user.id)
        if created_template:
            template_dict = created_template.to_dict()
            print(f"✅ 创建模板成功，visibility: {template_dict.get('visibility')}")
            
            # 验证visibility字段是否正确保存
            if template_dict.get('visibility') == 'private':
                print("✅ visibility字段正确保存")
                return True
            else:
                print(f"❌ visibility字段保存错误，期望: private，实际: {template_dict.get('visibility')}")
                return False
        else:
            print("❌ 创建模板失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def test_set_records_processing():
    """测试组记录数据的处理"""
    
    print("\n🧪 测试组记录数据处理")
    
    # 获取数据库会话
    db: Session = next(get_db())
    
    try:
        from app.services.set_record_manager import SetRecordManager
        
        set_manager = SetRecordManager(db)
        
        # 测试临时ID解析
        test_cases = [
            ("221", 221),  # 纯数字字符串
            (221, 221),    # 整数
            ("221_set_1748521342915_0", None),  # 临时ID格式，应该返回None
            ("set_1748521342915_0", None),      # 纯临时ID，应该返回None
        ]
        
        for test_id, expected in test_cases:
            result = set_manager._resolve_set_record_id(test_id)
            if result == expected or (expected is None and result is None):
                print(f"✅ ID解析测试通过: {test_id} -> {result}")
            else:
                print(f"❌ ID解析测试失败: {test_id} -> {result}, 期望: {expected}")
                return False
        
        # 测试重量和次数解析
        weight_tests = [
            ("20kg", 20.0),
            ("15.5", 15.5),
            ("", None),
            (None, None)
        ]
        
        for weight_str, expected in weight_tests:
            result = set_manager._parse_weight(weight_str)
            if result == expected:
                print(f"✅ 重量解析测试通过: {weight_str} -> {result}")
            else:
                print(f"❌ 重量解析测试失败: {weight_str} -> {result}, 期望: {expected}")
                return False
        
        reps_tests = [
            ("10", 10),
            ("10-12", 11),  # 范围取中间值
            ("", None),
            (None, None)
        ]
        
        for reps_str, expected in reps_tests:
            result = set_manager._parse_reps(reps_str)
            if result == expected:
                print(f"✅ 次数解析测试通过: {reps_str} -> {result}")
            else:
                print(f"❌ 次数解析测试失败: {reps_str} -> {result}, 期望: {expected}")
                return False
        
        print("✅ 组记录数据处理测试全部通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def test_template_update_with_set_records():
    """测试包含组记录的模板更新"""
    
    print("\n🧪 测试包含组记录的模板更新")
    
    # 获取数据库会话
    db: Session = next(get_db())
    
    try:
        from app.services.training_template_service import TrainingTemplateService
        from app.schemas.training_plan import TrainingTemplateCreate, TrainingTemplateExerciseCreate, SetRecordCreate
        
        template_service = TrainingTemplateService(db)
        
        # 查找现有模板
        user = db.query(User).first()
        if not user:
            print("❌ 没有找到测试用户")
            return False
        
        existing_template = db.query(WorkoutTemplate).filter(
            WorkoutTemplate.user_id == user.id
        ).first()
        
        if not existing_template:
            print("❌ 没有找到现有模板")
            return False
        
        print(f"✅ 找到现有模板: {existing_template.name} (ID: {existing_template.id})")
        
        # 创建包含组记录的更新数据
        update_data = TrainingTemplateCreate(
            name="更新后的模板",
            description="更新后的描述",
            estimated_duration=90,
            target_body_parts=[2, 6, 5],  # 胸部、肩部、手臂
            training_scenario="gym",
            visibility="public",  # 更新visibility
            notes="更新后的备注",
            exercises=[
                TrainingTemplateExerciseCreate(
                    exercise_id=1,
                    sets=4,
                    reps="8-10",
                    weight="25",
                    rest_seconds=90,
                    exercise_type="weight_reps",
                    set_records=[
                        SetRecordCreate(
                            id="221_set_1748521342915_0",  # 临时ID
                            set_number=1,
                            set_type="normal",
                            weight=25.0,
                            reps=8,
                            completed=False
                        ),
                        SetRecordCreate(
                            id="222_set_1748521342915_1",  # 临时ID
                            set_number=2,
                            set_type="normal",
                            weight=25.0,
                            reps=9,
                            completed=False
                        )
                    ]
                )
            ]
        )
        
        # 执行更新
        result = template_service.update_template(existing_template.id, update_data, user.id)
        
        if result:
            print(f"✅ 模板更新成功")
            template_dict = result.get('template', {})
            
            # 验证visibility字段
            if template_dict.get('visibility') == 'public':
                print("✅ visibility字段更新成功")
            else:
                print(f"❌ visibility字段更新失败，期望: public，实际: {template_dict.get('visibility')}")
                return False
            
            # 验证组记录
            exercises = template_dict.get('exercises', [])
            if exercises:
                first_exercise = exercises[0]
                set_records = first_exercise.get('set_records', [])
                print(f"✅ 组记录数量: {len(set_records)}")
                
                if set_records:
                    # 验证组记录ID是真实的数据库ID而不是临时ID
                    first_set = set_records[0]
                    set_id = first_set.get('id')
                    if isinstance(set_id, int) and set_id > 0:
                        print(f"✅ 组记录ID是真实的数据库ID: {set_id}")
                    else:
                        print(f"❌ 组记录ID不是真实的数据库ID: {set_id}")
                        return False
                else:
                    print("⚠️ 没有找到组记录数据")
            
            print("✅ 模板更新测试通过")
            return True
        else:
            print("❌ 模板更新失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 开始测试训练模板更新接口修复")
    
    success1 = test_visibility_field_support()
    success2 = test_set_records_processing()
    success3 = test_template_update_with_set_records()
    
    if success1 and success2 and success3:
        print("\n🎉 所有测试通过！修复成功！")
        print("\n📋 修复总结:")
        print("✅ 添加了visibility字段支持")
        print("✅ 完善了组记录数据的更新逻辑")
        print("✅ 正确处理临时ID格式")
        print("✅ 确保响应数据的完整性")
    else:
        print("\n❌ 部分测试失败")
        sys.exit(1)
