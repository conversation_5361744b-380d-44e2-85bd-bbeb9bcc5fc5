#!/usr/bin/env python3
"""
检查 workout_templates 表结构
"""

import sys
import os
sys.path.insert(0, '/home/<USER>/backend')

from app.db.session import get_db
from sqlalchemy import text

def check_table_structure():
    """检查 workout_templates 表结构"""
    
    print("🔍 检查 workout_templates 表结构")
    
    db = next(get_db())
    try:
        # 查询表结构
        result = db.execute(text("""
            SELECT column_name, data_type, is_nullable, column_default 
            FROM information_schema.columns 
            WHERE table_name = 'workout_templates' 
            ORDER BY ordinal_position;
        """))
        columns = result.fetchall()
        
        print(f"\n📋 workout_templates 表包含 {len(columns)} 个字段:")
        for col in columns:
            print(f"  • {col[0]}: {col[1]} (nullable: {col[2]}, default: {col[3]})")
        
        # 特别检查 visibility 字段
        visibility_found = any(col[0] == 'visibility' for col in columns)
        if visibility_found:
            print(f"\n✅ visibility 字段已成功添加到表中")
            return True
        else:
            print(f"\n❌ visibility 字段未找到")
            return False
            
    except Exception as e:
        print(f"❌ 检查表结构失败: {str(e)}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = check_table_structure()
    if not success:
        sys.exit(1)
