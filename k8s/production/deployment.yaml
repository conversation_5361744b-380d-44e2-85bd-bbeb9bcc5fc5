apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-assistant-production
  namespace: production
  labels:
    app: ai-assistant
    environment: production
    version: "${DEPLOYMENT_SHA}"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ai-assistant
      environment: production
  template:
    metadata:
      labels:
        app: ai-assistant
        environment: production
        version: "${DEPLOYMENT_SHA}"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: ai-assistant-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: ai-assistant
        image: "${IMAGE_TAG}"
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ai-assistant-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: ai-assistant-secrets
              key: redis-url
        - name: LLM_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-assistant-secrets
              key: llm-api-key
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: ai-assistant-secrets
              key: secret-key
        - name: CORS_ORIGINS
          valueFrom:
            configMapKeyRef:
              name: ai-assistant-config
              key: cors-origins
        - name: LOG_LEVEL
          value: "INFO"
        - name: WORKERS
          value: "4"
        resources:
          requests:
            cpu: 1000m
            memory: 2Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: uploads
          mountPath: /app/uploads
        - name: config
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: logs
        persistentVolumeClaim:
          claimName: ai-assistant-logs-pvc
      - name: uploads
        persistentVolumeClaim:
          claimName: ai-assistant-uploads-pvc
      - name: config
        configMap:
          name: ai-assistant-config
      nodeSelector:
        node-type: application
      tolerations:
      - key: "application"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - ai-assistant
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: ai-assistant-service
  namespace: production
  labels:
    app: ai-assistant
    environment: production
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: ai-assistant
    environment: production

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-assistant-config
  namespace: production
data:
  cors-origins: "https://fitness-ai.example.com,https://app.fitness-ai.example.com"
  log-level: "INFO"
  workers: "4"
  max-connections: "100"
  timeout: "30"

---
apiVersion: v1
kind: Secret
metadata:
  name: ai-assistant-secrets
  namespace: production
type: Opaque
data:
  # 这些值应该通过CI/CD流程或外部密钥管理系统注入
  database-url: ""  # base64编码的数据库连接字符串
  redis-url: ""     # base64编码的Redis连接字符串
  llm-api-key: ""   # base64编码的LLM API密钥
  secret-key: ""    # base64编码的应用密钥

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ai-assistant-sa
  namespace: production
  labels:
    app: ai-assistant

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: production
  name: ai-assistant-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ai-assistant-rolebinding
  namespace: production
subjects:
- kind: ServiceAccount
  name: ai-assistant-sa
  namespace: production
roleRef:
  kind: Role
  name: ai-assistant-role
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ai-assistant-logs-pvc
  namespace: production
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ai-assistant-uploads-pvc
  namespace: production
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  storageClassName: standard

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ai-assistant-hpa
  namespace: production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-assistant-production
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
