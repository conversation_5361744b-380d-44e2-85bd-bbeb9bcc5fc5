name: AI健身助手 CI/CD Pipeline

on:
  push:
    branches: [main, develop, feature/*]
  pull_request:
    branches: [main, develop]
  release:
    types: [published]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: fitness-ai-assistant

jobs:
  # 代码质量检查和测试
  test:
    name: 测试和代码质量检查
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Python环境
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          cache: 'pip'

      - name: 安装依赖
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt

      - name: 代码格式检查
        run: |
          black --check .
          isort --check-only .
          flake8 .

      - name: 类型检查
        run: mypy app/

      - name: 安全扫描
        run: |
          bandit -r app/
          safety check

      - name: 运行单元测试
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379/0
          ENVIRONMENT: test
        run: |
          pytest tests/unit/ -v --cov=app --cov-report=xml --cov-report=html

      - name: 运行集成测试
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379/0
          ENVIRONMENT: test
        run: |
          pytest tests/integration/ -v --cov-append --cov=app --cov-report=xml

      - name: 上传测试覆盖率
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: unittests
          name: codecov-umbrella

      - name: 生成测试报告
        if: always()
        run: |
          mkdir -p test-reports
          cp coverage.xml test-reports/
          cp -r htmlcov test-reports/

      - name: 上传测试报告
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: test-reports
          path: test-reports/

  # 构建Docker镜像
  build:
    name: 构建Docker镜像
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name != 'pull_request'
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录容器注册表
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 提取元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: 构建并推送镜像
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.prod
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name != 'pull_request'

    steps:
      - name: 运行Trivy漏洞扫描
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ needs.build.outputs.image-tag }}
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 上传Trivy扫描结果
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # 部署到测试环境
  deploy-staging:
    name: 部署到测试环境
    runs-on: ubuntu-latest
    needs: [build, security-scan]
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: 配置kubeconfig
        run: |
          mkdir -p $HOME/.kube
          echo "${{ secrets.KUBE_CONFIG_STAGING }}" | base64 -d > $HOME/.kube/config

      - name: 部署到Kubernetes
        run: |
          envsubst < k8s/staging/deployment.yaml | kubectl apply -f -
          kubectl rollout status deployment/ai-assistant-staging -n staging
        env:
          IMAGE_TAG: ${{ needs.build.outputs.image-tag }}

      - name: 运行健康检查
        run: |
          kubectl wait --for=condition=ready pod -l app=ai-assistant-staging -n staging --timeout=300s
          kubectl get pods -n staging

  # 部署到生产环境
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [build, security-scan]
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: 配置kubeconfig
        run: |
          mkdir -p $HOME/.kube
          echo "${{ secrets.KUBE_CONFIG_PRODUCTION }}" | base64 -d > $HOME/.kube/config

      - name: 蓝绿部署
        run: |
          # 创建新版本部署
          envsubst < k8s/production/deployment.yaml | kubectl apply -f -
          
          # 等待新版本就绪
          kubectl rollout status deployment/ai-assistant-production -n production
          
          # 切换流量
          kubectl patch service ai-assistant-service -n production -p '{"spec":{"selector":{"version":"'${{ github.sha }}'"}}}'
          
          # 验证部署
          kubectl get pods -n production
        env:
          IMAGE_TAG: ${{ needs.build.outputs.image-tag }}
          DEPLOYMENT_SHA: ${{ github.sha }}

      - name: 运行生产环境健康检查
        run: |
          kubectl wait --for=condition=ready pod -l app=ai-assistant-production -n production --timeout=600s
          
          # 运行健康检查
          HEALTH_URL=$(kubectl get service ai-assistant-service -n production -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
          curl -f http://$HEALTH_URL/health || exit 1

      - name: 清理旧版本
        run: |
          # 保留最近3个版本
          kubectl get replicasets -n production -o jsonpath='{.items[*].metadata.name}' | \
          tr ' ' '\n' | sort -r | tail -n +4 | \
          xargs -r kubectl delete replicaset -n production

  # 性能测试
  performance-test:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/develop'

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 运行性能测试
        run: |
          # 使用k6进行性能测试
          docker run --rm -v $PWD/tests/performance:/scripts \
            grafana/k6 run /scripts/load-test.js \
            --env STAGING_URL=${{ secrets.STAGING_URL }}

      - name: 上传性能测试报告
        uses: actions/upload-artifact@v3
        with:
          name: performance-report
          path: performance-report.html

  # 通知
  notify:
    name: 部署通知
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: always()

    steps:
      - name: 发送部署通知
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          text: |
            AI健身助手部署状态: ${{ job.status }}
            分支: ${{ github.ref }}
            提交: ${{ github.sha }}
            作者: ${{ github.actor }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
