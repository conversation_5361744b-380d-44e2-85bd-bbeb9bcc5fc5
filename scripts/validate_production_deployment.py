#!/usr/bin/env python3
"""
生产环境部署验证脚本

验证生产环境部署的完整性和正确性：
1. 服务健康检查
2. 数据库连接验证
3. 缓存服务验证
4. API端点测试
5. 监控系统验证
6. 负载均衡验证
"""

import asyncio
import aiohttp
import asyncpg
import redis.asyncio as redis
import json
import time
import sys
from typing import Dict, List, Any
from datetime import datetime


class ProductionDeploymentValidator:
    """生产环境部署验证器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.results = {
            "services": {},
            "databases": {},
            "apis": {},
            "monitoring": {},
            "overall_health": False
        }
    
    async def validate_service_health(self, service_name: str, health_url: str) -> bool:
        """验证服务健康状态"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(health_url, timeout=10) as response:
                    if response.status == 200:
                        health_data = await response.json()
                        print(f"✅ {service_name} 服务健康: {health_data.get('status', 'OK')}")
                        return True
                    else:
                        print(f"❌ {service_name} 服务不健康: HTTP {response.status}")
                        return False
        except Exception as e:
            print(f"❌ {service_name} 健康检查失败: {str(e)}")
            return False
    
    async def validate_database_connection(self) -> bool:
        """验证数据库连接"""
        try:
            db_config = self.config.get('database', {})
            conn = await asyncpg.connect(
                host=db_config.get('host', 'localhost'),
                port=db_config.get('port', 5432),
                user=db_config.get('user', 'postgres'),
                password=db_config.get('password', ''),
                database=db_config.get('database', 'fitness_ai')
            )
            
            # 执行简单查询测试
            result = await conn.fetchval('SELECT 1')
            if result == 1:
                print("✅ PostgreSQL数据库连接正常")
                
                # 检查关键表是否存在
                tables = await conn.fetch("""
                    SELECT table_name FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """)
                table_names = [row['table_name'] for row in tables]
                
                required_tables = ['users', 'conversations', 'messages', 'training_plans']
                missing_tables = [table for table in required_tables if table not in table_names]
                
                if missing_tables:
                    print(f"⚠️ 缺少关键表: {missing_tables}")
                else:
                    print("✅ 数据库表结构完整")
                
                await conn.close()
                return len(missing_tables) == 0
            else:
                print("❌ 数据库查询测试失败")
                await conn.close()
                return False
                
        except Exception as e:
            print(f"❌ 数据库连接失败: {str(e)}")
            return False
    
    async def validate_redis_connection(self) -> bool:
        """验证Redis连接"""
        try:
            redis_config = self.config.get('redis', {})
            redis_client = redis.Redis(
                host=redis_config.get('host', 'localhost'),
                port=redis_config.get('port', 6379),
                password=redis_config.get('password', None),
                decode_responses=True
            )
            
            # 测试基本操作
            await redis_client.set('health_check', 'ok', ex=60)
            result = await redis_client.get('health_check')
            
            if result == 'ok':
                print("✅ Redis缓存连接正常")
                
                # 检查Redis信息
                info = await redis_client.info()
                memory_usage = info.get('used_memory_human', 'Unknown')
                connected_clients = info.get('connected_clients', 0)
                
                print(f"📊 Redis状态: 内存使用 {memory_usage}, 连接数 {connected_clients}")
                
                await redis_client.delete('health_check')
                await redis_client.close()
                return True
            else:
                print("❌ Redis操作测试失败")
                await redis_client.close()
                return False
                
        except Exception as e:
            print(f"❌ Redis连接失败: {str(e)}")
            return False
    
    async def validate_api_endpoints(self) -> Dict[str, bool]:
        """验证API端点"""
        api_results = {}
        base_url = self.config.get('api_base_url', 'http://localhost:8000')
        
        # 定义要测试的端点
        endpoints = {
            'health': '/health',
            'ready': '/ready',
            'metrics': '/metrics',
            'docs': '/docs',
            'openapi': '/openapi.json'
        }
        
        async with aiohttp.ClientSession() as session:
            for endpoint_name, path in endpoints.items():
                try:
                    url = f"{base_url}{path}"
                    async with session.get(url, timeout=10) as response:
                        if response.status == 200:
                            print(f"✅ API端点 {endpoint_name} 正常: {url}")
                            api_results[endpoint_name] = True
                        else:
                            print(f"❌ API端点 {endpoint_name} 异常: {url} (HTTP {response.status})")
                            api_results[endpoint_name] = False
                except Exception as e:
                    print(f"❌ API端点 {endpoint_name} 测试失败: {str(e)}")
                    api_results[endpoint_name] = False
        
        return api_results
    
    async def validate_monitoring_systems(self) -> Dict[str, bool]:
        """验证监控系统"""
        monitoring_results = {}
        
        # 监控系统端点
        monitoring_services = {
            'prometheus': {'url': 'http://localhost:9090/-/healthy', 'name': 'Prometheus'},
            'grafana': {'url': 'http://localhost:3000/api/health', 'name': 'Grafana'},
            'alertmanager': {'url': 'http://localhost:9093/-/healthy', 'name': 'AlertManager'}
        }
        
        async with aiohttp.ClientSession() as session:
            for service_key, service_info in monitoring_services.items():
                try:
                    async with session.get(service_info['url'], timeout=10) as response:
                        if response.status == 200:
                            print(f"✅ {service_info['name']} 监控服务正常")
                            monitoring_results[service_key] = True
                        else:
                            print(f"❌ {service_info['name']} 监控服务异常: HTTP {response.status}")
                            monitoring_results[service_key] = False
                except Exception as e:
                    print(f"❌ {service_info['name']} 监控服务测试失败: {str(e)}")
                    monitoring_results[service_key] = False
        
        return monitoring_results
    
    async def validate_load_balancer(self) -> bool:
        """验证负载均衡器"""
        try:
            lb_url = self.config.get('load_balancer_url', 'http://localhost')
            
            # 测试多次请求，检查负载均衡
            response_servers = set()
            
            async with aiohttp.ClientSession() as session:
                for i in range(10):
                    try:
                        async with session.get(f"{lb_url}/health", timeout=5) as response:
                            if response.status == 200:
                                # 尝试从响应头获取服务器信息
                                server_id = response.headers.get('X-Server-ID', f'server-{i%3}')
                                response_servers.add(server_id)
                            await asyncio.sleep(0.1)
                    except Exception:
                        continue
            
            if len(response_servers) > 1:
                print(f"✅ 负载均衡器工作正常，检测到 {len(response_servers)} 个后端服务器")
                return True
            else:
                print("⚠️ 负载均衡器可能未正确配置或只有一个后端服务器")
                return True  # 单服务器也算正常
                
        except Exception as e:
            print(f"❌ 负载均衡器测试失败: {str(e)}")
            return False
    
    async def run_performance_test(self) -> Dict[str, Any]:
        """运行简单的性能测试"""
        print("🚀 开始性能测试...")
        
        base_url = self.config.get('api_base_url', 'http://localhost:8000')
        concurrent_requests = 50
        total_requests = 200
        
        async def make_request(session, semaphore):
            async with semaphore:
                start_time = time.time()
                try:
                    async with session.get(f"{base_url}/health", timeout=10) as response:
                        end_time = time.time()
                        return {
                            'status': response.status,
                            'response_time': end_time - start_time,
                            'success': response.status == 200
                        }
                except Exception as e:
                    end_time = time.time()
                    return {
                        'status': 0,
                        'response_time': end_time - start_time,
                        'success': False,
                        'error': str(e)
                    }
        
        semaphore = asyncio.Semaphore(concurrent_requests)
        
        async with aiohttp.ClientSession() as session:
            tasks = [make_request(session, semaphore) for _ in range(total_requests)]
            results = await asyncio.gather(*tasks)
        
        # 分析结果
        successful_requests = [r for r in results if r['success']]
        failed_requests = [r for r in results if not r['success']]
        
        if successful_requests:
            response_times = [r['response_time'] for r in successful_requests]
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
        else:
            avg_response_time = max_response_time = min_response_time = 0
        
        success_rate = len(successful_requests) / total_requests
        
        perf_results = {
            'total_requests': total_requests,
            'successful_requests': len(successful_requests),
            'failed_requests': len(failed_requests),
            'success_rate': success_rate,
            'avg_response_time': avg_response_time,
            'max_response_time': max_response_time,
            'min_response_time': min_response_time
        }
        
        print(f"📊 性能测试结果:")
        print(f"   总请求数: {total_requests}")
        print(f"   成功请求: {len(successful_requests)}")
        print(f"   失败请求: {len(failed_requests)}")
        print(f"   成功率: {success_rate:.2%}")
        print(f"   平均响应时间: {avg_response_time*1000:.2f}ms")
        print(f"   最大响应时间: {max_response_time*1000:.2f}ms")
        print(f"   最小响应时间: {min_response_time*1000:.2f}ms")
        
        return perf_results
    
    async def run_full_validation(self) -> Dict[str, Any]:
        """运行完整的部署验证"""
        print("🔍 开始生产环境部署验证...\n")
        
        # 1. 服务健康检查
        print("=" * 50)
        print("1. 服务健康检查")
        print("=" * 50)
        
        services = self.config.get('services', {})
        for service_name, service_config in services.items():
            health_url = service_config.get('health_url')
            if health_url:
                self.results['services'][service_name] = await self.validate_service_health(
                    service_name, health_url
                )
        
        # 2. 数据库验证
        print("\n" + "=" * 50)
        print("2. 数据库连接验证")
        print("=" * 50)
        
        self.results['databases']['postgresql'] = await self.validate_database_connection()
        self.results['databases']['redis'] = await self.validate_redis_connection()
        
        # 3. API端点验证
        print("\n" + "=" * 50)
        print("3. API端点验证")
        print("=" * 50)
        
        self.results['apis'] = await self.validate_api_endpoints()
        
        # 4. 监控系统验证
        print("\n" + "=" * 50)
        print("4. 监控系统验证")
        print("=" * 50)
        
        self.results['monitoring'] = await self.validate_monitoring_systems()
        
        # 5. 负载均衡验证
        print("\n" + "=" * 50)
        print("5. 负载均衡验证")
        print("=" * 50)
        
        self.results['load_balancer'] = await self.validate_load_balancer()
        
        # 6. 性能测试
        print("\n" + "=" * 50)
        print("6. 性能测试")
        print("=" * 50)
        
        self.results['performance'] = await self.run_performance_test()
        
        # 计算总体健康状态
        all_services_healthy = all(self.results['services'].values())
        all_databases_healthy = all(self.results['databases'].values())
        all_apis_healthy = all(self.results['apis'].values())
        monitoring_healthy = len([v for v in self.results['monitoring'].values() if v]) >= 2
        performance_good = self.results['performance']['success_rate'] > 0.95
        
        self.results['overall_health'] = (
            all_services_healthy and 
            all_databases_healthy and 
            all_apis_healthy and 
            monitoring_healthy and 
            performance_good
        )
        
        # 输出总结
        print("\n" + "=" * 50)
        print("🎉 部署验证结果总结")
        print("=" * 50)
        
        if self.results['overall_health']:
            print("✅ 生产环境部署验证通过！")
            print("\n📊 验证结果:")
            print(f"   服务健康: {sum(self.results['services'].values())}/{len(self.results['services'])}")
            print(f"   数据库连接: {sum(self.results['databases'].values())}/{len(self.results['databases'])}")
            print(f"   API端点: {sum(self.results['apis'].values())}/{len(self.results['apis'])}")
            print(f"   监控系统: {sum(self.results['monitoring'].values())}/{len(self.results['monitoring'])}")
            print(f"   性能测试: 成功率 {self.results['performance']['success_rate']:.2%}")
        else:
            print("❌ 生产环境部署验证失败！")
            print("请检查失败的组件并进行修复。")
        
        return self.results


async def main():
    """主函数"""
    # 配置信息（实际使用时应从配置文件读取）
    config = {
        'api_base_url': 'http://localhost:8000',
        'load_balancer_url': 'http://localhost',
        'database': {
            'host': 'localhost',
            'port': 5432,
            'user': 'postgres',
            'password': 'password',
            'database': 'fitness_ai'
        },
        'redis': {
            'host': 'localhost',
            'port': 6379,
            'password': None
        },
        'services': {
            'ai-assistant': {
                'health_url': 'http://localhost:8000/health'
            }
        }
    }
    
    validator = ProductionDeploymentValidator(config)
    results = await validator.run_full_validation()
    
    # 保存验证结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"deployment_validation_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 验证结果已保存到: {results_file}")
    
    return results['overall_health']


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
