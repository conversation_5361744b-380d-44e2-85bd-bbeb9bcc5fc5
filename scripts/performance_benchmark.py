#!/usr/bin/env python3
"""
性能基准测试脚本

执行全面的性能基准测试：
1. 响应时间测试
2. 吞吐量测试
3. 并发性能测试
4. 资源使用率测试
5. 压力测试
6. 长时间稳定性测试
"""

import asyncio
import aiohttp
import psutil
import time
import json
import sys
import statistics
from typing import Dict, List, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    response_times: List[float]
    success_count: int
    error_count: int
    start_time: float
    end_time: float
    
    @property
    def total_requests(self) -> int:
        return self.success_count + self.error_count
    
    @property
    def success_rate(self) -> float:
        return self.success_count / self.total_requests if self.total_requests > 0 else 0
    
    @property
    def avg_response_time(self) -> float:
        return statistics.mean(self.response_times) if self.response_times else 0
    
    @property
    def p50_response_time(self) -> float:
        return statistics.median(self.response_times) if self.response_times else 0
    
    @property
    def p95_response_time(self) -> float:
        if not self.response_times:
            return 0
        sorted_times = sorted(self.response_times)
        index = int(len(sorted_times) * 0.95)
        return sorted_times[index] if index < len(sorted_times) else sorted_times[-1]
    
    @property
    def p99_response_time(self) -> float:
        if not self.response_times:
            return 0
        sorted_times = sorted(self.response_times)
        index = int(len(sorted_times) * 0.99)
        return sorted_times[index] if index < len(sorted_times) else sorted_times[-1]
    
    @property
    def rps(self) -> float:
        duration = self.end_time - self.start_time
        return self.total_requests / duration if duration > 0 else 0


class PerformanceBenchmark:
    """性能基准测试器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.base_url = config.get('base_url', 'http://localhost:8000')
        self.results = {}
    
    async def single_request(self, session: aiohttp.ClientSession, endpoint: str, 
                           method: str = 'GET', data: Dict = None) -> Tuple[bool, float, int]:
        """执行单个请求"""
        start_time = time.time()
        try:
            url = f"{self.base_url}{endpoint}"
            
            if method.upper() == 'GET':
                async with session.get(url, timeout=30) as response:
                    end_time = time.time()
                    return response.status < 400, end_time - start_time, response.status
            elif method.upper() == 'POST':
                async with session.post(url, json=data, timeout=30) as response:
                    end_time = time.time()
                    return response.status < 400, end_time - start_time, response.status
            else:
                end_time = time.time()
                return False, end_time - start_time, 0
                
        except Exception as e:
            end_time = time.time()
            return False, end_time - start_time, 0
    
    async def response_time_test(self) -> PerformanceMetrics:
        """响应时间测试"""
        print("🚀 开始响应时间测试...")
        
        test_endpoints = [
            '/health',
            '/ready',
            '/metrics',
            '/api/v1/users/profile',
            '/api/v1/ai-assistant/chat'
        ]
        
        response_times = []
        success_count = 0
        error_count = 0
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            for endpoint in test_endpoints:
                for _ in range(20):  # 每个端点测试20次
                    if endpoint == '/api/v1/ai-assistant/chat':
                        # POST请求测试
                        test_data = {
                            "message": "测试消息",
                            "conversation_id": "test_conv_001"
                        }
                        success, response_time, status = await self.single_request(
                            session, endpoint, 'POST', test_data
                        )
                    else:
                        # GET请求测试
                        success, response_time, status = await self.single_request(
                            session, endpoint, 'GET'
                        )
                    
                    response_times.append(response_time)
                    if success:
                        success_count += 1
                    else:
                        error_count += 1
                    
                    await asyncio.sleep(0.1)  # 避免过于频繁的请求
        
        end_time = time.time()
        
        metrics = PerformanceMetrics(
            response_times=response_times,
            success_count=success_count,
            error_count=error_count,
            start_time=start_time,
            end_time=end_time
        )
        
        print(f"✅ 响应时间测试完成:")
        print(f"   平均响应时间: {metrics.avg_response_time*1000:.2f}ms")
        print(f"   P50响应时间: {metrics.p50_response_time*1000:.2f}ms")
        print(f"   P95响应时间: {metrics.p95_response_time*1000:.2f}ms")
        print(f"   P99响应时间: {metrics.p99_response_time*1000:.2f}ms")
        print(f"   成功率: {metrics.success_rate:.2%}")
        
        return metrics
    
    async def throughput_test(self, concurrent_users: int = 50, duration: int = 60) -> PerformanceMetrics:
        """吞吐量测试"""
        print(f"🚀 开始吞吐量测试 (并发用户: {concurrent_users}, 持续时间: {duration}秒)...")
        
        response_times = []
        success_count = 0
        error_count = 0
        start_time = time.time()
        
        async def worker(session: aiohttp.ClientSession, semaphore: asyncio.Semaphore):
            """工作协程"""
            nonlocal response_times, success_count, error_count
            
            while time.time() - start_time < duration:
                async with semaphore:
                    success, response_time, status = await self.single_request(
                        session, '/health', 'GET'
                    )
                    
                    response_times.append(response_time)
                    if success:
                        success_count += 1
                    else:
                        error_count += 1
                
                await asyncio.sleep(0.01)  # 短暂休息
        
        semaphore = asyncio.Semaphore(concurrent_users)
        
        async with aiohttp.ClientSession() as session:
            tasks = [worker(session, semaphore) for _ in range(concurrent_users)]
            await asyncio.gather(*tasks)
        
        end_time = time.time()
        
        metrics = PerformanceMetrics(
            response_times=response_times,
            success_count=success_count,
            error_count=error_count,
            start_time=start_time,
            end_time=end_time
        )
        
        print(f"✅ 吞吐量测试完成:")
        print(f"   总请求数: {metrics.total_requests}")
        print(f"   RPS: {metrics.rps:.2f}")
        print(f"   平均响应时间: {metrics.avg_response_time*1000:.2f}ms")
        print(f"   成功率: {metrics.success_rate:.2%}")
        
        return metrics
    
    async def concurrent_test(self, max_users: int = 200, step: int = 20, step_duration: int = 30) -> Dict[int, PerformanceMetrics]:
        """并发性能测试"""
        print(f"🚀 开始并发性能测试 (最大用户: {max_users}, 步长: {step}, 每步持续: {step_duration}秒)...")
        
        results = {}
        
        for concurrent_users in range(step, max_users + 1, step):
            print(f"\n📊 测试并发用户数: {concurrent_users}")
            
            response_times = []
            success_count = 0
            error_count = 0
            start_time = time.time()
            
            async def worker(session: aiohttp.ClientSession, semaphore: asyncio.Semaphore):
                nonlocal response_times, success_count, error_count
                
                while time.time() - start_time < step_duration:
                    async with semaphore:
                        success, response_time, status = await self.single_request(
                            session, '/health', 'GET'
                        )
                        
                        response_times.append(response_time)
                        if success:
                            success_count += 1
                        else:
                            error_count += 1
                    
                    await asyncio.sleep(0.01)
            
            semaphore = asyncio.Semaphore(concurrent_users)
            
            async with aiohttp.ClientSession() as session:
                tasks = [worker(session, semaphore) for _ in range(concurrent_users)]
                await asyncio.gather(*tasks)
            
            end_time = time.time()
            
            metrics = PerformanceMetrics(
                response_times=response_times,
                success_count=success_count,
                error_count=error_count,
                start_time=start_time,
                end_time=end_time
            )
            
            results[concurrent_users] = metrics
            
            print(f"   RPS: {metrics.rps:.2f}")
            print(f"   平均响应时间: {metrics.avg_response_time*1000:.2f}ms")
            print(f"   成功率: {metrics.success_rate:.2%}")
            
            # 如果成功率低于90%，停止测试
            if metrics.success_rate < 0.9:
                print(f"⚠️ 成功率低于90%，停止并发测试")
                break
        
        print("✅ 并发性能测试完成")
        return results
    
    async def resource_usage_test(self, duration: int = 300) -> Dict[str, List[float]]:
        """资源使用率测试"""
        print(f"🚀 开始资源使用率测试 (持续时间: {duration}秒)...")
        
        cpu_usage = []
        memory_usage = []
        disk_usage = []
        network_io = []
        
        start_time = time.time()
        
        # 同时运行负载和监控
        async def load_generator():
            """负载生成器"""
            async with aiohttp.ClientSession() as session:
                while time.time() - start_time < duration:
                    tasks = []
                    for _ in range(10):  # 每次发送10个并发请求
                        task = self.single_request(session, '/health', 'GET')
                        tasks.append(task)
                    
                    await asyncio.gather(*tasks)
                    await asyncio.sleep(1)
        
        def monitor_resources():
            """资源监控"""
            while time.time() - start_time < duration:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                cpu_usage.append(cpu_percent)
                
                # 内存使用率
                memory = psutil.virtual_memory()
                memory_usage.append(memory.percent)
                
                # 磁盘使用率
                disk = psutil.disk_usage('/')
                disk_usage.append(disk.percent)
                
                # 网络IO
                network = psutil.net_io_counters()
                network_io.append(network.bytes_sent + network.bytes_recv)
                
                time.sleep(5)  # 每5秒采样一次
        
        # 启动负载生成和资源监控
        import threading
        monitor_thread = threading.Thread(target=monitor_resources)
        monitor_thread.start()
        
        await load_generator()
        
        monitor_thread.join()
        
        resource_metrics = {
            'cpu_usage': cpu_usage,
            'memory_usage': memory_usage,
            'disk_usage': disk_usage,
            'network_io': network_io
        }
        
        print(f"✅ 资源使用率测试完成:")
        print(f"   平均CPU使用率: {statistics.mean(cpu_usage):.2f}%")
        print(f"   最大CPU使用率: {max(cpu_usage):.2f}%")
        print(f"   平均内存使用率: {statistics.mean(memory_usage):.2f}%")
        print(f"   最大内存使用率: {max(memory_usage):.2f}%")
        
        return resource_metrics
    
    async def stress_test(self, max_concurrent: int = 500, ramp_up_time: int = 120) -> PerformanceMetrics:
        """压力测试"""
        print(f"🚀 开始压力测试 (最大并发: {max_concurrent}, 爬坡时间: {ramp_up_time}秒)...")
        
        response_times = []
        success_count = 0
        error_count = 0
        start_time = time.time()
        
        async def worker(session: aiohttp.ClientSession, worker_id: int):
            """工作协程"""
            nonlocal response_times, success_count, error_count
            
            # 计算启动延迟（爬坡）
            start_delay = (worker_id / max_concurrent) * ramp_up_time
            await asyncio.sleep(start_delay)
            
            # 运行5分钟
            worker_start = time.time()
            while time.time() - worker_start < 300:  # 5分钟
                success, response_time, status = await self.single_request(
                    session, '/health', 'GET'
                )
                
                response_times.append(response_time)
                if success:
                    success_count += 1
                else:
                    error_count += 1
                
                await asyncio.sleep(0.1)
        
        async with aiohttp.ClientSession() as session:
            tasks = [worker(session, i) for i in range(max_concurrent)]
            await asyncio.gather(*tasks)
        
        end_time = time.time()
        
        metrics = PerformanceMetrics(
            response_times=response_times,
            success_count=success_count,
            error_count=error_count,
            start_time=start_time,
            end_time=end_time
        )
        
        print(f"✅ 压力测试完成:")
        print(f"   总请求数: {metrics.total_requests}")
        print(f"   RPS: {metrics.rps:.2f}")
        print(f"   平均响应时间: {metrics.avg_response_time*1000:.2f}ms")
        print(f"   P95响应时间: {metrics.p95_response_time*1000:.2f}ms")
        print(f"   成功率: {metrics.success_rate:.2%}")
        
        return metrics
    
    async def run_full_benchmark(self) -> Dict[str, Any]:
        """运行完整的性能基准测试"""
        print("🔍 开始性能基准测试...\n")
        
        benchmark_results = {}
        
        # 1. 响应时间测试
        print("=" * 60)
        print("1. 响应时间测试")
        print("=" * 60)
        benchmark_results['response_time'] = await self.response_time_test()
        
        # 2. 吞吐量测试
        print("\n" + "=" * 60)
        print("2. 吞吐量测试")
        print("=" * 60)
        benchmark_results['throughput'] = await self.throughput_test()
        
        # 3. 并发性能测试
        print("\n" + "=" * 60)
        print("3. 并发性能测试")
        print("=" * 60)
        benchmark_results['concurrent'] = await self.concurrent_test()
        
        # 4. 资源使用率测试
        print("\n" + "=" * 60)
        print("4. 资源使用率测试")
        print("=" * 60)
        benchmark_results['resource_usage'] = await self.resource_usage_test()
        
        # 5. 压力测试
        print("\n" + "=" * 60)
        print("5. 压力测试")
        print("=" * 60)
        benchmark_results['stress'] = await self.stress_test()
        
        # 性能评估
        print("\n" + "=" * 60)
        print("🎉 性能基准测试结果评估")
        print("=" * 60)
        
        # 评估标准
        performance_targets = {
            'avg_response_time': 0.05,  # 50ms
            'p95_response_time': 0.1,   # 100ms
            'p99_response_time': 0.2,   # 200ms
            'min_rps': 500,             # 500 RPS
            'min_success_rate': 0.99    # 99%
        }
        
        # 检查是否达到性能目标
        response_time_metrics = benchmark_results['response_time']
        throughput_metrics = benchmark_results['throughput']
        
        performance_check = {
            'avg_response_time': response_time_metrics.avg_response_time <= performance_targets['avg_response_time'],
            'p95_response_time': response_time_metrics.p95_response_time <= performance_targets['p95_response_time'],
            'p99_response_time': response_time_metrics.p99_response_time <= performance_targets['p99_response_time'],
            'rps': throughput_metrics.rps >= performance_targets['min_rps'],
            'success_rate': throughput_metrics.success_rate >= performance_targets['min_success_rate']
        }
        
        passed_checks = sum(1 for check in performance_check.values() if check)
        total_checks = len(performance_check)
        
        print(f"\n📊 性能目标达成情况:")
        for metric, passed in performance_check.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {metric}: {'通过' if passed else '未通过'}")
        
        print(f"\n🎯 总体性能评分: {passed_checks}/{total_checks} ({passed_checks/total_checks:.1%})")
        
        if passed_checks >= total_checks * 0.8:  # 80%以上通过
            print("\n🎉 性能基准测试通过！系统性能达到预期目标。")
            benchmark_results['overall_pass'] = True
        else:
            print("\n⚠️ 性能基准测试未完全通过，建议进行性能优化。")
            benchmark_results['overall_pass'] = False
        
        benchmark_results['performance_check'] = performance_check
        
        return benchmark_results


async def main():
    """主函数"""
    config = {
        'base_url': 'http://localhost:8000'
    }
    
    benchmark = PerformanceBenchmark(config)
    results = await benchmark.run_full_benchmark()
    
    # 保存测试结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"performance_benchmark_{timestamp}.json"
    
    # 序列化结果（处理PerformanceMetrics对象）
    serializable_results = {}
    for key, value in results.items():
        if isinstance(value, PerformanceMetrics):
            serializable_results[key] = {
                'total_requests': value.total_requests,
                'success_count': value.success_count,
                'error_count': value.error_count,
                'success_rate': value.success_rate,
                'avg_response_time': value.avg_response_time,
                'p50_response_time': value.p50_response_time,
                'p95_response_time': value.p95_response_time,
                'p99_response_time': value.p99_response_time,
                'rps': value.rps
            }
        elif isinstance(value, dict) and all(isinstance(v, PerformanceMetrics) for v in value.values()):
            serializable_results[key] = {
                k: {
                    'total_requests': v.total_requests,
                    'success_count': v.success_count,
                    'error_count': v.error_count,
                    'success_rate': v.success_rate,
                    'avg_response_time': v.avg_response_time,
                    'rps': v.rps
                } for k, v in value.items()
            }
        else:
            serializable_results[key] = value
    
    with open(results_file, 'w') as f:
        json.dump(serializable_results, f, indent=2, default=str)
    
    print(f"\n📄 性能测试结果已保存到: {results_file}")
    
    return results.get('overall_pass', False)


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
