#!/bin/bash

# 生产环境入口脚本
set -e

echo "🚀 启动生产环境 AI 健身助手系统..."

# 等待数据库就绪
echo "⏳ 等待数据库连接..."
while ! pg_isready -h ${DATABASE_HOST:-postgres} -p ${DATABASE_PORT:-5432} -U ${DATABASE_USER:-postgres}; do
    echo "数据库未就绪，等待5秒..."
    sleep 5
done
echo "✅ 数据库连接成功"

# 等待Redis就绪
echo "⏳ 等待Redis连接..."
while ! redis-cli -h ${REDIS_HOST:-redis} -p ${REDIS_PORT:-6379} ping > /dev/null 2>&1; do
    echo "Redis未就绪，等待3秒..."
    sleep 3
done
echo "✅ Redis连接成功"

# 运行数据库迁移
echo "🔄 执行数据库迁移..."
alembic upgrade head
echo "✅ 数据库迁移完成"

# 初始化缓存
echo "🔄 初始化缓存..."
python -c "
import asyncio
from app.services.ai_assistant.integration.cache_manager import IntelligentCacheManager
from app.core.database import get_redis_client

async def init_cache():
    redis_client = await get_redis_client()
    cache_manager = IntelligentCacheManager(redis_client)
    
    # 预热关键缓存
    await cache_manager.preload_cache('user_profile', ['default_user'])
    await cache_manager.preload_cache('exercise_data', ['basic_exercises'])
    print('缓存预热完成')

asyncio.run(init_cache())
"
echo "✅ 缓存初始化完成"

# 检查系统健康状态
echo "🔍 检查系统健康状态..."
python -c "
import asyncio
from app.services.ai_assistant.integration.performance_monitor import IntelligentPerformanceMonitor

async def health_check():
    monitor = IntelligentPerformanceMonitor()
    await monitor.start_monitoring()
    print('性能监控已启动')

asyncio.run(health_check())
"
echo "✅ 系统健康检查完成"

# 设置日志目录权限
mkdir -p /app/logs
chmod 755 /app/logs

# 启动应用
echo "🎯 启动 AI 健身助手服务..."
echo "环境: ${ENVIRONMENT:-production}"
echo "工作进程数: ${WORKERS:-4}"
echo "监听端口: ${PORT:-8000}"

# 执行传入的命令
exec "$@"
