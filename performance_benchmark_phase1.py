#!/usr/bin/env python3
"""
阶段一性能基准测试

验证阶段一实现的性能指标：
- 状态转换性能 (<10ms)
- 意图处理性能 (<200ms)
- 并发处理能力 (>50 QPS)
- 缓存命中率 (>80%)
"""

import asyncio
import time
import statistics
from concurrent.futures import ThreadPoolExecutor
import sys
import os

# 导入测试模块
sys.path.insert(0, os.path.abspath('.'))
from test_phase1_implementation import (
    SimpleStateAdapter, SimpleIntentProcessor, SimpleStateManager, SimpleUnifiedState
)

class PerformanceBenchmark:
    def __init__(self):
        self.state_adapter = SimpleStateAdapter()
        self.intent_processor = SimpleIntentProcessor()
        self.state_manager = SimpleStateManager()
        
        # 性能指标收集
        self.metrics = {
            "state_conversion_times": [],
            "intent_processing_times": [],
            "state_management_times": [],
            "cache_hits": 0,
            "cache_misses": 0
        }
    
    async def benchmark_state_conversion(self, iterations=1000):
        """状态转换性能基准测试"""
        print(f"🧪 状态转换性能测试 ({iterations} 次迭代)...")
        
        sample_state = {
            "conversation_id": "perf_test",
            "user_id": "user_001",
            "intent": "training_plan",
            "confidence": 0.9,
            "user_profile": {"age": 25, "gender": "male"},
            "training_params": {"goal": "muscle_gain"}
        }
        
        times = []
        for i in range(iterations):
            start_time = time.perf_counter()
            
            # 转换为统一状态
            unified_state = await self.state_adapter.convert_to_unified(
                sample_state, "dict", f"perf_test_{i}"
            )
            
            # 转换回字典格式
            dict_state = await self.state_adapter.convert_from_unified(
                unified_state, "dict"
            )
            
            end_time = time.perf_counter()
            times.append((end_time - start_time) * 1000)  # 转换为毫秒
        
        self.metrics["state_conversion_times"] = times
        
        avg_time = statistics.mean(times)
        p95_time = statistics.quantiles(times, n=20)[18]  # 95th percentile
        p99_time = statistics.quantiles(times, n=100)[98]  # 99th percentile
        
        print(f"  平均转换时间: {avg_time:.2f}ms")
        print(f"  P95转换时间: {p95_time:.2f}ms")
        print(f"  P99转换时间: {p99_time:.2f}ms")
        print(f"  目标: <10ms, 实际: {'✅' if avg_time < 10 else '❌'}")
        
        return avg_time < 10
    
    async def benchmark_intent_processing(self, iterations=500):
        """意图处理性能基准测试"""
        print(f"\n🧪 意图处理性能测试 ({iterations} 次迭代)...")
        
        test_messages = [
            "我想制定一个训练计划",
            "推荐一些胸部动作",
            "如何正确深蹲？",
            "增肌期间应该吃什么？",
            "你好，今天天气不错"
        ]
        
        times = []
        for i in range(iterations):
            message = test_messages[i % len(test_messages)]
            state = SimpleUnifiedState(conversation_id=f"intent_test_{i}")
            
            start_time = time.perf_counter()
            
            result_state = await self.intent_processor.process_intent(message, state)
            
            end_time = time.perf_counter()
            times.append((end_time - start_time) * 1000)  # 转换为毫秒
        
        self.metrics["intent_processing_times"] = times
        
        avg_time = statistics.mean(times)
        p95_time = statistics.quantiles(times, n=20)[18]
        p99_time = statistics.quantiles(times, n=100)[98]
        
        print(f"  平均处理时间: {avg_time:.2f}ms")
        print(f"  P95处理时间: {p95_time:.2f}ms")
        print(f"  P99处理时间: {p99_time:.2f}ms")
        print(f"  目标: <200ms, 实际: {'✅' if avg_time < 200 else '❌'}")
        
        return avg_time < 200
    
    async def benchmark_state_management(self, iterations=800):
        """状态管理性能基准测试"""
        print(f"\n🧪 状态管理性能测试 ({iterations} 次迭代)...")
        
        times = []
        cache_hits = 0
        cache_misses = 0
        
        for i in range(iterations):
            conversation_id = f"state_test_{i % 100}"  # 重复使用ID来测试缓存
            
            start_time = time.perf_counter()
            
            # 获取状态
            state = await self.state_manager.get_current_state(conversation_id)
            
            # 修改状态
            state["intent"] = "test_intent"
            state["confidence"] = 0.8
            
            # 保存状态
            await self.state_manager.save_state(state)
            
            end_time = time.perf_counter()
            times.append((end_time - start_time) * 1000)
            
            # 检查是否命中缓存
            if conversation_id in self.state_manager.state_cache:
                cache_hits += 1
            else:
                cache_misses += 1
        
        self.metrics["state_management_times"] = times
        self.metrics["cache_hits"] = cache_hits
        self.metrics["cache_misses"] = cache_misses
        
        avg_time = statistics.mean(times)
        cache_hit_rate = cache_hits / (cache_hits + cache_misses) * 100
        
        print(f"  平均管理时间: {avg_time:.2f}ms")
        print(f"  缓存命中率: {cache_hit_rate:.1f}%")
        print(f"  目标: <100ms, 实际: {'✅' if avg_time < 100 else '❌'}")
        print(f"  缓存目标: >80%, 实际: {'✅' if cache_hit_rate > 80 else '❌'}")
        
        return avg_time < 100 and cache_hit_rate > 80
    
    async def benchmark_concurrent_processing(self, concurrent_users=100, requests_per_user=10):
        """并发处理性能基准测试"""
        print(f"\n🧪 并发处理性能测试 ({concurrent_users} 用户, {requests_per_user} 请求/用户)...")
        
        async def user_session(user_id: int):
            """模拟用户会话"""
            session_times = []
            for req_id in range(requests_per_user):
                conversation_id = f"concurrent_{user_id}_{req_id}"
                
                start_time = time.perf_counter()
                
                # 获取状态
                state = await self.state_manager.get_current_state(conversation_id)
                
                # 处理意图
                message = f"用户{user_id}的第{req_id}个请求"
                state = await self.intent_processor.process_intent(message, state)
                
                # 保存状态
                await self.state_manager.save_state(state)
                
                end_time = time.perf_counter()
                session_times.append(end_time - start_time)
            
            return session_times
        
        # 创建并发任务
        start_time = time.perf_counter()
        
        tasks = [user_session(user_id) for user_id in range(concurrent_users)]
        results = await asyncio.gather(*tasks)
        
        end_time = time.perf_counter()
        
        # 计算性能指标
        total_requests = concurrent_users * requests_per_user
        total_time = end_time - start_time
        qps = total_requests / total_time
        
        all_times = [time for user_times in results for time in user_times]
        avg_response_time = statistics.mean(all_times) * 1000  # 转换为毫秒
        
        print(f"  总请求数: {total_requests}")
        print(f"  总耗时: {total_time:.2f}s")
        print(f"  QPS: {qps:.1f}")
        print(f"  平均响应时间: {avg_response_time:.2f}ms")
        print(f"  目标: >50 QPS, 实际: {'✅' if qps > 50 else '❌'}")
        
        return qps > 50
    
    async def benchmark_memory_usage(self):
        """内存使用基准测试"""
        print(f"\n🧪 内存使用测试...")
        
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建大量状态对象
        states = []
        for i in range(1000):
            state = SimpleUnifiedState(
                conversation_id=f"memory_test_{i}",
                user_id=f"user_{i}",
                intent="training_plan",
                confidence=0.9,
                user_profile={"age": 25, "gender": "male", "data": "x" * 100},
                training_params={"goal": "muscle_gain", "data": "y" * 100}
            )
            states.append(state)
        
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory
        
        # 清理
        del states
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"  初始内存: {initial_memory:.1f}MB")
        print(f"  峰值内存: {peak_memory:.1f}MB")
        print(f"  内存增长: {memory_increase:.1f}MB")
        print(f"  清理后内存: {final_memory:.1f}MB")
        print(f"  目标: <100MB增长, 实际: {'✅' if memory_increase < 100 else '❌'}")
        
        return memory_increase < 100
    
    def generate_report(self):
        """生成性能报告"""
        print("\n📊 性能基准测试报告")
        print("=" * 50)
        
        if self.metrics["state_conversion_times"]:
            avg_conversion = statistics.mean(self.metrics["state_conversion_times"])
            print(f"状态转换平均时间: {avg_conversion:.2f}ms")
        
        if self.metrics["intent_processing_times"]:
            avg_intent = statistics.mean(self.metrics["intent_processing_times"])
            print(f"意图处理平均时间: {avg_intent:.2f}ms")
        
        if self.metrics["state_management_times"]:
            avg_state = statistics.mean(self.metrics["state_management_times"])
            print(f"状态管理平均时间: {avg_state:.2f}ms")
        
        if self.metrics["cache_hits"] + self.metrics["cache_misses"] > 0:
            cache_rate = self.metrics["cache_hits"] / (self.metrics["cache_hits"] + self.metrics["cache_misses"]) * 100
            print(f"缓存命中率: {cache_rate:.1f}%")

async def main():
    """主测试函数"""
    print("🚀 阶段一性能基准测试开始\n")
    
    benchmark = PerformanceBenchmark()
    
    # 运行所有基准测试
    results = []
    
    try:
        results.append(await benchmark.benchmark_state_conversion())
        results.append(await benchmark.benchmark_intent_processing())
        results.append(await benchmark.benchmark_state_management())
        results.append(await benchmark.benchmark_concurrent_processing())
        results.append(await benchmark.benchmark_memory_usage())
        
        # 生成报告
        benchmark.generate_report()
        
        # 总结
        passed_tests = sum(results)
        total_tests = len(results)
        
        print(f"\n🎯 性能基准测试总结:")
        print(f"通过测试: {passed_tests}/{total_tests}")
        
        if passed_tests == total_tests:
            print("🎉 所有性能基准测试通过！")
            print("\n✅ 阶段一性能目标全部达成:")
            print("• 状态转换性能 <10ms ✅")
            print("• 意图处理性能 <200ms ✅")
            print("• 并发处理能力 >50 QPS ✅")
            print("• 缓存命中率 >80% ✅")
            print("• 内存使用控制 <100MB ✅")
        else:
            print("⚠️ 部分性能基准测试未通过，需要优化")
        
        return passed_tests == total_tests
        
    except Exception as e:
        print(f"\n❌ 性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
