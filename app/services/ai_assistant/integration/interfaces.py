"""
智能健身AI助手整合模块核心接口定义

定义了统一的接口规范，确保不同组件间的兼容性和可扩展性。
基于现有系统的接口设计，提供向后兼容的统一抽象。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Union, List, Optional, AsyncGenerator
from ..langgraph.state_definitions import UnifiedFitnessState

# 使用现有的状态定义
from ...state_definitions import ConversationState

# 创建简单的意图结果类
class IntentResult:
    def __init__(self, intent: str, confidence: float, parameters: Dict[str, Any] = None,
                 sub_intents: List[str] = None, recognition_time: float = 0, error: str = None):
        self.intent = intent
        self.confidence = confidence
        self.parameters = parameters or {}
        self.sub_intents = sub_intents or []
        self.recognition_time = recognition_time
        self.error = error


class StateManagerInterface(ABC):
    """状态管理器统一接口
    
    整合LangGraph检查点存储和原始系统缓存机制，
    提供统一的状态获取、保存和删除功能。
    """
    
    @abstractmethod
    async def get_current_state(self, conversation_id: str) -> UnifiedFitnessState:
        """获取当前会话状态
        
        Args:
            conversation_id: 会话ID
            
        Returns:
            UnifiedFitnessState: 统一状态对象
        """
        pass
    
    @abstractmethod
    async def save_state(self, state: UnifiedFitnessState) -> bool:
        """保存会话状态
        
        Args:
            state: 统一状态对象
            
        Returns:
            bool: 保存是否成功
        """
        pass
    
    @abstractmethod
    async def delete_state(self, conversation_id: str) -> bool:
        """删除会话状态
        
        Args:
            conversation_id: 会话ID
            
        Returns:
            bool: 删除是否成功
        """
        pass
    
    @abstractmethod
    async def migrate_from_legacy(self, conversation_id: str) -> Optional[UnifiedFitnessState]:
        """从原始系统迁移状态
        
        Args:
            conversation_id: 会话ID
            
        Returns:
            Optional[UnifiedFitnessState]: 迁移后的状态，如果不存在则返回None
        """
        pass


class IntentProcessorInterface(ABC):
    """意图处理器统一接口
    
    整合原始意图识别器和LangGraph路由节点，
    提供三层意图处理策略：识别 -> 路由 -> 处理。
    """
    
    @abstractmethod
    async def process_intent(self, message: str, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """处理用户意图
        
        Args:
            message: 用户消息
            state: 当前状态
            
        Returns:
            UnifiedFitnessState: 处理后的状态
        """
        pass
    
    @abstractmethod
    async def recognize_intent(self, message: str, context: Dict[str, Any]) -> IntentResult:
        """识别用户意图
        
        Args:
            message: 用户消息
            context: 上下文信息
            
        Returns:
            IntentResult: 意图识别结果
        """
        pass
    
    @abstractmethod
    async def route_to_handler(self, intent: str, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """路由到对应的处理器
        
        Args:
            intent: 识别出的意图
            state: 当前状态
            
        Returns:
            UnifiedFitnessState: 路由后的状态
        """
        pass


class StateAdapterInterface(ABC):
    """状态适配器统一接口
    
    处理三套系统间的状态转换：
    - LangGraph ConversationState
    - 原始系统状态字典
    - 统一 UnifiedFitnessState
    """
    
    @abstractmethod
    async def convert_to_unified(self, 
                               source_state: Union[Dict, ConversationState],
                               source_type: str,
                               conversation_id: str = None) -> UnifiedFitnessState:
        """转换为统一状态格式
        
        Args:
            source_state: 源状态对象
            source_type: 源状态类型 ("conversation_state", "dict", "langgraph")
            conversation_id: 会话ID（可选）
            
        Returns:
            UnifiedFitnessState: 统一状态对象
        """
        pass
    
    @abstractmethod
    async def convert_from_unified(self, 
                                 unified_state: UnifiedFitnessState,
                                 target_type: str) -> Union[Dict, ConversationState]:
        """从统一状态转换为目标格式
        
        Args:
            unified_state: 统一状态对象
            target_type: 目标格式类型 ("conversation_state", "dict", "langgraph")
            
        Returns:
            Union[Dict, ConversationState]: 目标格式的状态对象
        """
        pass
    
    @abstractmethod
    async def validate_state(self, state: UnifiedFitnessState) -> bool:
        """验证状态格式正确性
        
        Args:
            state: 待验证的状态对象
            
        Returns:
            bool: 验证是否通过
        """
        pass


class ParameterManagerInterface(ABC):
    """参数管理器统一接口
    
    整合现有的参数提取器和验证器，
    提供统一的参数收集和验证功能。
    """
    
    @abstractmethod
    async def collect_user_info(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """收集用户基础信息
        
        Args:
            state: 当前状态
            
        Returns:
            UnifiedFitnessState: 更新后的状态
        """
        pass
    
    @abstractmethod
    async def collect_training_params(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """收集训练参数
        
        Args:
            state: 当前状态
            
        Returns:
            UnifiedFitnessState: 更新后的状态
        """
        pass
    
    @abstractmethod
    async def validate_parameters(self, params: Dict[str, Any], intent: str) -> Dict[str, Any]:
        """验证参数完整性
        
        Args:
            params: 待验证的参数
            intent: 意图类型
            
        Returns:
            Dict[str, Any]: 验证后的参数
        """
        pass


class StreamingProcessorInterface(ABC):
    """流式处理器接口
    
    基于现有WebSocket实现，提供统一的流式响应处理。
    """
    
    @abstractmethod
    async def stream_response(self, state: UnifiedFitnessState) -> AsyncGenerator[Dict[str, Any], None]:
        """流式响应处理
        
        Args:
            state: 当前状态
            
        Yields:
            Dict[str, Any]: 流式响应块
        """
        pass
    
    @abstractmethod
    async def handle_interruption(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """处理流式响应中断
        
        Args:
            state: 当前状态
            
        Returns:
            UnifiedFitnessState: 处理中断后的状态
        """
        pass


class ErrorHandlerInterface(ABC):
    """错误处理器接口
    
    提供统一的错误分类、处理和恢复机制。
    """
    
    @abstractmethod
    async def handle_error(self, error: Exception, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """处理错误
        
        Args:
            error: 异常对象
            state: 当前状态
            
        Returns:
            UnifiedFitnessState: 错误处理后的状态
        """
        pass
    
    @abstractmethod
    def classify_error(self, error: Exception) -> str:
        """分类错误类型
        
        Args:
            error: 异常对象
            
        Returns:
            str: 错误类型
        """
        pass
