"""
工作流编排器

统一业务流程管理和编排，整合阶段一和阶段二的所有组件：
- 统一状态管理（阶段一）
- 意图处理系统（阶段一）
- 参数收集系统（阶段二）
- 流式处理系统（阶段二）

实现工作流执行成功率>99%的目标。
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from enum import Enum

from .state_adapter import IntegratedStateAdapter
from .state_manager import IntegratedStateManager
from .intent_processor import IntegratedIntentProcessor
from .parameter_manager import EnhancedParameterManager
from .streaming_processor import StreamingProcessor

from ..langgraph.state_definitions import UnifiedFitnessState
from ...logger.logger import get_logger

logger = get_logger(__name__)


class WorkflowStage(Enum):
    """工作流阶段枚举"""
    INITIALIZATION = "initialization"
    INTENT_RECOGNITION = "intent_recognition"
    PARAMETER_COLLECTION = "parameter_collection"
    PROCESSING = "processing"
    RESPONSE_GENERATION = "response_generation"
    STREAMING = "streaming"
    COMPLETION = "completion"
    ERROR_HANDLING = "error_handling"


class WorkflowStatus(Enum):
    """工作流状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    INTERRUPTED = "interrupted"


class WorkflowOrchestrator:
    """工作流编排器 - 统一业务流程管理"""
    
    def __init__(self, db_session, llm_service):
        self.db_session = db_session
        self.llm_service = llm_service
        
        # 初始化所有组件
        self.state_adapter = IntegratedStateAdapter()
        self.state_manager = IntegratedStateManager(db_session)
        self.intent_processor = IntegratedIntentProcessor(llm_service, db_session)
        self.parameter_manager = EnhancedParameterManager(db_session, llm_service)
        self.streaming_processor = StreamingProcessor(db_session, llm_service)
        
        # 工作流配置
        self.max_workflow_time = 300  # 最大工作流时间（秒）
        self.max_retry_attempts = 3   # 最大重试次数
        self.stage_timeout = 60       # 单个阶段超时时间（秒）
        
        # 工作流定义
        self.workflow_stages = self._define_workflow_stages()
        
        # 活跃工作流管理
        self.active_workflows = {}  # conversation_id -> workflow_info
        
        logger.info("工作流编排器初始化完成")
    
    def _define_workflow_stages(self) -> Dict[WorkflowStage, Callable]:
        """定义工作流阶段和对应的处理函数"""
        return {
            WorkflowStage.INITIALIZATION: self._stage_initialization,
            WorkflowStage.INTENT_RECOGNITION: self._stage_intent_recognition,
            WorkflowStage.PARAMETER_COLLECTION: self._stage_parameter_collection,
            WorkflowStage.PROCESSING: self._stage_processing,
            WorkflowStage.RESPONSE_GENERATION: self._stage_response_generation,
            WorkflowStage.STREAMING: self._stage_streaming,
            WorkflowStage.COMPLETION: self._stage_completion,
            WorkflowStage.ERROR_HANDLING: self._stage_error_handling
        }
    
    async def execute_workflow(self, message: str, conversation_id: str, 
                             user_id: str = None, stream: bool = False) -> UnifiedFitnessState:
        """执行完整的工作流"""
        workflow_id = f"{conversation_id}_{int(time.time())}"
        
        try:
            logger.info(f"开始执行工作流: {workflow_id}")
            
            # 初始化工作流信息
            workflow_info = {
                "workflow_id": workflow_id,
                "conversation_id": conversation_id,
                "user_id": user_id,
                "message": message,
                "stream": stream,
                "status": WorkflowStatus.RUNNING,
                "current_stage": WorkflowStage.INITIALIZATION,
                "start_time": time.time(),
                "stage_times": {},
                "retry_count": 0,
                "error_count": 0
            }
            
            self.active_workflows[conversation_id] = workflow_info
            
            # 执行工作流阶段
            state = await self._execute_workflow_stages(workflow_info)
            
            # 标记工作流完成
            workflow_info["status"] = WorkflowStatus.COMPLETED
            workflow_info["end_time"] = time.time()
            workflow_info["duration"] = workflow_info["end_time"] - workflow_info["start_time"]
            
            logger.info(f"工作流执行完成: {workflow_id}, 耗时: {workflow_info['duration']:.2f}s")
            
            return state
            
        except Exception as e:
            logger.error(f"工作流执行失败: {workflow_id}, 错误: {str(e)}")
            
            # 标记工作流失败
            if conversation_id in self.active_workflows:
                self.active_workflows[conversation_id]["status"] = WorkflowStatus.FAILED
                self.active_workflows[conversation_id]["error"] = str(e)
            
            # 执行错误处理
            return await self._handle_workflow_error(conversation_id, str(e))
        
        finally:
            # 清理工作流信息
            if conversation_id in self.active_workflows:
                del self.active_workflows[conversation_id]
    
    async def _execute_workflow_stages(self, workflow_info: Dict[str, Any]) -> UnifiedFitnessState:
        """执行工作流各个阶段"""
        conversation_id = workflow_info["conversation_id"]
        current_stage = WorkflowStage.INITIALIZATION
        state = None
        
        while current_stage != WorkflowStage.COMPLETION:
            try:
                # 更新当前阶段
                workflow_info["current_stage"] = current_stage
                stage_start_time = time.time()
                
                logger.debug(f"执行工作流阶段: {current_stage.value}, 会话: {conversation_id}")
                
                # 执行当前阶段
                stage_handler = self.workflow_stages.get(current_stage)
                if stage_handler:
                    state = await stage_handler(workflow_info, state)
                else:
                    raise ValueError(f"未找到阶段处理器: {current_stage}")
                
                # 记录阶段执行时间
                stage_duration = time.time() - stage_start_time
                workflow_info["stage_times"][current_stage.value] = stage_duration
                
                # 检查阶段超时
                if stage_duration > self.stage_timeout:
                    logger.warning(f"阶段执行超时: {current_stage.value}, 耗时: {stage_duration:.2f}s")
                
                # 确定下一个阶段
                current_stage = await self._determine_next_stage(current_stage, state, workflow_info)
                
                # 检查工作流总超时
                total_time = time.time() - workflow_info["start_time"]
                if total_time > self.max_workflow_time:
                    logger.error(f"工作流总超时: {conversation_id}, 耗时: {total_time:.2f}s")
                    raise TimeoutError("工作流执行超时")
                
            except Exception as e:
                logger.error(f"工作流阶段执行失败: {current_stage.value}, 错误: {str(e)}")
                
                # 增加错误计数
                workflow_info["error_count"] += 1
                
                # 检查是否需要重试
                if workflow_info["retry_count"] < self.max_retry_attempts:
                    workflow_info["retry_count"] += 1
                    logger.info(f"重试工作流阶段: {current_stage.value}, 第{workflow_info['retry_count']}次")
                    continue
                else:
                    # 转到错误处理阶段
                    current_stage = WorkflowStage.ERROR_HANDLING
                    state = await self._stage_error_handling(workflow_info, state, str(e))
                    break
        
        return state
    
    async def _stage_initialization(self, workflow_info: Dict[str, Any], 
                                  state: Optional[UnifiedFitnessState]) -> UnifiedFitnessState:
        """初始化阶段"""
        conversation_id = workflow_info["conversation_id"]
        user_id = workflow_info["user_id"]
        message = workflow_info["message"]
        
        # 获取或创建状态
        state = await self.state_manager.get_current_state(conversation_id)
        
        # 更新基础信息
        state["conversation_id"] = conversation_id
        state["user_id"] = user_id or state.get("user_id", "")
        state["session_id"] = conversation_id
        state["timestamp"] = datetime.now()
        
        # 添加用户消息
        from ...state_definitions import AnyMessage
        user_message = AnyMessage(role="user", content=message)
        state["messages"].append(user_message)
        
        # 重置处理状态
        state["processing_start_time"] = time.time()
        state["current_state_name"] = "processing"
        state["processing_system"] = "workflow_orchestrator"
        
        logger.debug(f"初始化阶段完成: {conversation_id}")
        return state
    
    async def _stage_intent_recognition(self, workflow_info: Dict[str, Any], 
                                      state: UnifiedFitnessState) -> UnifiedFitnessState:
        """意图识别阶段"""
        message = workflow_info["message"]
        
        # 使用意图处理器识别意图
        state = await self.intent_processor.process_intent(message, state)
        
        logger.debug(f"意图识别完成: {state['intent']}, 置信度: {state['confidence']}")
        return state
    
    async def _stage_parameter_collection(self, workflow_info: Dict[str, Any], 
                                        state: UnifiedFitnessState) -> UnifiedFitnessState:
        """参数收集阶段"""
        intent = state["intent"]
        
        # 根据意图类型收集不同的参数
        if intent in ["training_plan", "exercise_recommendation", "daily_workout_plan", "weekly_workout_plan"]:
            # 收集训练参数
            state = await self.parameter_manager.collect_training_params(state)
        
        # 检查是否需要收集用户信息
        if not state["user_profile"] or len(state["user_profile"]) < 2:
            state = await self.parameter_manager.collect_user_info(state)
        
        logger.debug(f"参数收集阶段完成: {state['conversation_id']}")
        return state
    
    async def _stage_processing(self, workflow_info: Dict[str, Any], 
                              state: UnifiedFitnessState) -> UnifiedFitnessState:
        """处理阶段"""
        # 检查参数是否完整
        flow_state = state.get("flow_state", {})
        
        if flow_state.get("collecting_user_info") or flow_state.get("collecting_training_params"):
            # 仍在收集参数，跳过处理阶段
            state["response_type"] = "parameter_collection"
            return state
        
        # 参数收集完成，进行业务处理
        intent = state["intent"]
        
        if intent == "training_plan":
            state["response_content"] = "正在为您制定个性化训练计划..."
            state["structured_data"] = {
                "plan_type": "training_plan",
                "body_part": state["training_params"].get("body_part", "全身"),
                "scenario": state["training_params"].get("scenario", "健身房")
            }
        elif intent == "exercise_recommendation":
            state["response_content"] = "正在为您推荐适合的运动..."
            state["structured_data"] = {
                "recommendation_type": "exercise",
                "body_part": state["training_params"].get("body_part", "全身")
            }
        elif intent == "fitness_qa":
            state["response_content"] = "正在为您查找相关的健身知识..."
            state["structured_data"] = {"qa_type": "fitness"}
        else:
            state["response_content"] = "我是您的健身助手，有什么可以帮您的吗？"
            state["structured_data"] = {"chat_type": "general"}
        
        state["response_type"] = "text"
        state["current_state_name"] = "processed"
        
        logger.debug(f"处理阶段完成: {intent}")
        return state
    
    async def _stage_response_generation(self, workflow_info: Dict[str, Any], 
                                       state: UnifiedFitnessState) -> UnifiedFitnessState:
        """响应生成阶段"""
        # 生成AI响应消息
        from ...state_definitions import AnyMessage
        ai_message = AnyMessage(role="assistant", content=state["response_content"])
        state["messages"].append(ai_message)
        
        # 更新状态
        state["current_state_name"] = "response_generated"
        
        logger.debug(f"响应生成完成: {state['conversation_id']}")
        return state
    
    async def _stage_streaming(self, workflow_info: Dict[str, Any], 
                             state: UnifiedFitnessState) -> UnifiedFitnessState:
        """流式处理阶段"""
        if not workflow_info.get("stream", False):
            # 非流式模式，跳过此阶段
            return state
        
        # 启动流式处理
        state["flow_state"]["streaming_enabled"] = True
        state["response_type"] = "streaming"
        
        logger.debug(f"流式处理阶段完成: {state['conversation_id']}")
        return state
    
    async def _stage_completion(self, workflow_info: Dict[str, Any], 
                              state: UnifiedFitnessState) -> UnifiedFitnessState:
        """完成阶段"""
        # 保存最终状态
        await self.state_manager.save_state(state)
        
        # 更新状态
        state["current_state_name"] = "completed"
        state["processing_end_time"] = time.time()
        
        # 计算处理时间
        processing_time = state["processing_end_time"] - state["processing_start_time"]
        state["node_execution_times"]["total_workflow"] = processing_time
        
        logger.debug(f"工作流完成: {state['conversation_id']}, 处理时间: {processing_time:.2f}s")
        return state
    
    async def _stage_error_handling(self, workflow_info: Dict[str, Any], 
                                  state: Optional[UnifiedFitnessState], 
                                  error: str = None) -> UnifiedFitnessState:
        """错误处理阶段"""
        conversation_id = workflow_info["conversation_id"]
        
        if state is None:
            # 创建错误状态
            state = await self.state_manager.get_current_state(conversation_id)
        
        # 更新错误信息
        state["error_count"] = state.get("error_count", 0) + 1
        state["response_content"] = "抱歉，处理您的请求时遇到了问题，请稍后重试。"
        state["response_type"] = "error"
        state["current_state_name"] = "error"
        
        if error:
            state["flow_state"]["last_error"] = error
        
        logger.error(f"工作流错误处理: {conversation_id}, 错误: {error}")
        return state
    
    async def _determine_next_stage(self, current_stage: WorkflowStage, 
                                  state: UnifiedFitnessState, 
                                  workflow_info: Dict[str, Any]) -> WorkflowStage:
        """确定下一个工作流阶段"""
        flow_state = state.get("flow_state", {})
        
        if current_stage == WorkflowStage.INITIALIZATION:
            return WorkflowStage.INTENT_RECOGNITION
        
        elif current_stage == WorkflowStage.INTENT_RECOGNITION:
            return WorkflowStage.PARAMETER_COLLECTION
        
        elif current_stage == WorkflowStage.PARAMETER_COLLECTION:
            # 检查是否仍在收集参数
            if flow_state.get("collecting_user_info") or flow_state.get("collecting_training_params"):
                return WorkflowStage.COMPLETION  # 参数收集中，直接完成
            else:
                return WorkflowStage.PROCESSING
        
        elif current_stage == WorkflowStage.PROCESSING:
            return WorkflowStage.RESPONSE_GENERATION
        
        elif current_stage == WorkflowStage.RESPONSE_GENERATION:
            if workflow_info.get("stream", False):
                return WorkflowStage.STREAMING
            else:
                return WorkflowStage.COMPLETION
        
        elif current_stage == WorkflowStage.STREAMING:
            return WorkflowStage.COMPLETION
        
        else:
            return WorkflowStage.COMPLETION
    
    async def _handle_workflow_error(self, conversation_id: str, error: str) -> UnifiedFitnessState:
        """处理工作流错误"""
        try:
            state = await self.state_manager.get_current_state(conversation_id)
            state["error_count"] = state.get("error_count", 0) + 1
            state["response_content"] = "系统遇到问题，正在恢复中..."
            state["response_type"] = "error"
            state["flow_state"]["workflow_error"] = error
            
            await self.state_manager.save_state(state)
            return state
        
        except Exception as e:
            logger.error(f"错误处理失败: {conversation_id}, 错误: {str(e)}")
            # 返回最小化错误状态
            return UnifiedFitnessState(
                conversation_id=conversation_id,
                user_id="",
                session_id=conversation_id,
                timestamp=datetime.now(),
                intent="error",
                confidence=0.0,
                intent_parameters={},
                user_profile={},
                training_params={},
                fitness_goals=[],
                flow_state={"error": error},
                current_state_name="error",
                current_node="",
                processing_system="workflow_orchestrator",
                response_content="系统遇到严重问题，请联系技术支持。",
                response_type="error",
                structured_data={},
                error_count=1,
                retry_count=0,
                processing_start_time=time.time(),
                node_execution_times={},
                parallel_results=[],
                selected_result=None,
                messages=[]
            )
    
    def get_workflow_status(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流状态"""
        return self.active_workflows.get(conversation_id)
    
    def get_active_workflows(self) -> Dict[str, Dict[str, Any]]:
        """获取所有活跃的工作流"""
        return self.active_workflows.copy()
    
    async def interrupt_workflow(self, conversation_id: str) -> bool:
        """中断指定的工作流"""
        if conversation_id in self.active_workflows:
            self.active_workflows[conversation_id]["status"] = WorkflowStatus.INTERRUPTED
            logger.info(f"工作流已中断: {conversation_id}")
            return True
        return False
