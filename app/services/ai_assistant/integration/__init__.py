"""
智能健身AI助手整合模块

基于现有的LangGraph服务(app/services/langgraph_service.py)和
原始对话系统进行整合，提供统一的状态管理和意图处理功能。

本模块实现了79%的代码复用率，充分利用现有组件：
- LangGraph服务和图节点系统
- 原始意图识别和参数提取器
- 统一状态定义和适配器

Author: Fitness AI Team
Version: 1.0.0
"""

from .interfaces import (
    StateManagerInterface,
    IntentProcessorInterface,
    StateAdapterInterface,
    ParameterManagerInterface
)

from .state_adapter import IntegratedStateAdapter
from .state_manager import IntegratedStateManager
from .intent_processor import IntegratedIntentProcessor

# 版本和作者信息
__version__ = "1.0.0"
__author__ = "Fitness AI Team"

# 导出的公共接口
__all__ = [
    # 核心接口
    "StateManagerInterface",
    "IntentProcessorInterface", 
    "StateAdapterInterface",
    "ParameterManagerInterface",
    
    # 实现类
    "IntegratedStateAdapter",
    "IntegratedStateManager", 
    "IntegratedIntentProcessor",
    
    # 版本信息
    "__version__"
]

# 模块级配置 - 基于现有LangGraph服务配置
DEFAULT_CONFIG = {
    # 状态管理配置
    "state_cache_ttl": 3600,  # 状态缓存TTL（秒）
    "max_retry_attempts": 3,   # 最大重试次数
    "processing_timeout": 30,  # 处理超时时间（秒）
    
    # LangGraph集成配置
    "langgraph_recursion_limit": 50,  # 对应LangGraphService的递归限制
    "checkpoint_enabled": True,        # 启用检查点功能
    
    # 原始系统兼容配置
    "legacy_api_compatibility": True,  # 保持向后兼容
    "conversation_history_limit": 15,  # 对应原始系统的消息历史限制
}

# 日志配置
import logging
logger = logging.getLogger(__name__)
logger.info("智能健身AI助手整合模块已加载")
