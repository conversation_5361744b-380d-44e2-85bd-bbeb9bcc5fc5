"""
统一状态适配器

基于现有的LangGraph状态适配器扩展，处理三套系统间的状态转换：
- LangGraph ConversationState
- 原始系统状态字典  
- 统一 UnifiedFitnessState

复用现有组件，实现高效的状态转换和缓存机制。
"""

import time
import json
from datetime import datetime
from typing import Dict, Any, Union, List, Optional

# 使用现有的消息类型
from ...state_definitions import AnyMessage, ConversationState

from .interfaces import StateAdapterInterface
from ..langgraph.state_definitions import UnifiedFitnessState
from ..langgraph.adapters.state_adapter import StateAdapter as LangGraphStateAdapter
from ...logger.logger import get_logger

logger = get_logger(__name__)


class IntegratedStateAdapter(StateAdapterInterface):
    """整合状态适配器 - 基于现有适配器，扩展三套系统间的状态转换"""
    
    def __init__(self):
        # 使用现有的LangGraph状态适配器
        self.langgraph_adapter = LangGraphStateAdapter()
        
        # 转换缓存，防止重复计算
        self.conversion_cache = {}
        
        # 字段映射表
        self.field_mapping = self._init_field_mapping()
        
        # 缓存TTL（秒）
        self.cache_ttl = 300  # 5分钟
        
        logger.info("整合状态适配器初始化完成")
    
    def _init_field_mapping(self) -> Dict[str, Dict[str, str]]:
        """初始化字段映射表 - 基于现有系统结构"""
        return {
            "conversation_to_unified": {
                "conversation_id": "conversation_id",
                "user_id": "user_id", 
                "session_id": "session_id",
                "intent": "intent",
                "confidence": "confidence",
                "intent_parameters": "intent_parameters",
                "user_info": "user_profile",
                "training_params": "training_params",
                "meta_info": "flow_state",
                "current_state": "current_state_name",
                "messages": "messages",
                "response_content": "response_content"
            },
            "unified_to_conversation": {
                "conversation_id": "conversation_id",
                "user_id": "user_id",
                "session_id": "session_id", 
                "intent": "intent",
                "confidence": "confidence",
                "intent_parameters": "intent_parameters",
                "user_profile": "user_info",
                "training_params": "training_params",
                "flow_state": "meta_info",
                "current_state_name": "current_state",
                "response_content": "response_content"
            }
        }
    
    async def convert_to_unified(self, 
                               source_state: Union[Dict, ConversationState],
                               source_type: str,
                               conversation_id: str = None) -> UnifiedFitnessState:
        """转换为统一状态格式"""
        try:
            # 缓存键
            cache_key = f"{source_type}_{conversation_id}_{hash(str(source_state))}"
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                logger.debug(f"从缓存获取状态转换结果: {cache_key}")
                return cached_result
            
            # 根据源类型调用对应转换方法
            if source_type == "conversation_state":
                unified_state = await self._from_conversation_state(source_state, conversation_id)
            elif source_type == "dict":
                unified_state = await self._from_dict_state(source_state, conversation_id)
            elif source_type == "langgraph":
                # 使用现有的LangGraph适配器
                unified_state = self.langgraph_adapter.to_unified_state(
                    conversation_state=source_state if isinstance(source_state, dict) else source_state.__dict__,
                    conversation_id=conversation_id
                )
            else:
                raise ValueError(f"不支持的状态类型: {source_type}")
            
            # 验证转换结果
            await self.validate_state(unified_state)
            
            # 缓存结果（带TTL）
            self._set_to_cache(cache_key, unified_state)
            logger.info(f"状态转换成功: {source_type} -> unified")
            
            return unified_state
            
        except Exception as e:
            logger.error(f"状态转换失败: {source_type} -> unified, 错误: {str(e)}")
            # 返回默认状态
            return await self._create_default_state(conversation_id)
    
    async def convert_from_unified(self, 
                                 unified_state: UnifiedFitnessState,
                                 target_type: str) -> Union[Dict, ConversationState]:
        """从统一状态转换为目标格式"""
        try:
            if target_type == "conversation_state":
                return await self._to_conversation_state(unified_state)
            elif target_type == "dict":
                return await self._to_dict_state(unified_state)
            elif target_type == "langgraph":
                # 使用现有的LangGraph适配器
                return self.langgraph_adapter.from_unified_state(unified_state)
            else:
                raise ValueError(f"不支持的目标类型: {target_type}")
                
        except Exception as e:
            logger.error(f"状态转换失败: unified -> {target_type}, 错误: {str(e)}")
            return {}
    
    async def validate_state(self, state: UnifiedFitnessState) -> bool:
        """验证状态格式正确性"""
        required_fields = [
            "conversation_id", "user_id", "session_id", "timestamp",
            "intent", "confidence", "intent_parameters",
            "user_profile", "training_params", "flow_state",
            "current_state_name", "processing_system",
            "response_content", "response_type", "messages"
        ]
        
        for field in required_fields:
            if field not in state:
                raise ValueError(f"缺少必需字段: {field}")
        
        # 类型检查
        if not isinstance(state["confidence"], (int, float)):
            raise ValueError("confidence字段必须是数字类型")
        
        if not isinstance(state["messages"], list):
            raise ValueError("messages字段必须是列表类型")
        
        return True
    
    async def _from_conversation_state(self, source_state: ConversationState, 
                                     conversation_id: str) -> UnifiedFitnessState:
        """从对话状态转换为统一状态"""
        mapping = self.field_mapping["conversation_to_unified"]
        
        unified_state = UnifiedFitnessState(
            conversation_id=conversation_id or source_state.get("conversation_id", ""),
            user_id=source_state.get("user_id", ""),
            session_id=source_state.get("session_id", conversation_id or ""),
            timestamp=datetime.now(),
            
            intent=source_state.get("intent", ""),
            confidence=source_state.get("confidence", 0.0),
            intent_parameters=source_state.get("intent_parameters", {}),
            
            user_profile=source_state.get("user_info", {}),
            training_params=source_state.get("training_params", {}),
            fitness_goals=[],
            
            flow_state=source_state.get("meta_info", {}),
            current_state_name=source_state.get("current_state", "idle"),
            current_node="",
            processing_system="conversation_state",
            
            response_content=source_state.get("response_content", ""),
            response_type="text",
            structured_data={},
            
            error_count=0,
            retry_count=0,
            processing_start_time=time.time(),
            node_execution_times={},
            
            parallel_results=[],
            selected_result=None,
            
            messages=self._convert_messages(source_state.get("messages", []))
        )
        
        return unified_state
    
    async def _from_dict_state(self, source_state: Dict[str, Any], 
                             conversation_id: str) -> UnifiedFitnessState:
        """从字典状态转换为统一状态"""
        unified_state = UnifiedFitnessState(
            conversation_id=conversation_id or source_state.get("conversation_id", ""),
            user_id=source_state.get("user_id", ""),
            session_id=source_state.get("session_id", conversation_id or ""),
            timestamp=datetime.now(),
            
            intent=source_state.get("intent", ""),
            confidence=source_state.get("confidence", 0.0),
            intent_parameters=source_state.get("intent_parameters", {}),
            
            user_profile=source_state.get("user_profile", {}),
            training_params=source_state.get("training_params", {}),
            fitness_goals=source_state.get("fitness_goals", []),
            
            flow_state=source_state.get("flow_state", {}),
            current_state_name=source_state.get("current_state_name", "idle"),
            current_node=source_state.get("current_node", ""),
            processing_system="dict",
            
            response_content=source_state.get("response_content", ""),
            response_type=source_state.get("response_type", "text"),
            structured_data=source_state.get("structured_data", {}),
            
            error_count=source_state.get("error_count", 0),
            retry_count=source_state.get("retry_count", 0),
            processing_start_time=source_state.get("processing_start_time", time.time()),
            node_execution_times=source_state.get("node_execution_times", {}),
            
            parallel_results=source_state.get("parallel_results", []),
            selected_result=source_state.get("selected_result"),
            
            messages=self._convert_messages(source_state.get("messages", []))
        )
        
        return unified_state
    
    async def _to_conversation_state(self, unified_state: UnifiedFitnessState) -> ConversationState:
        """转换为对话状态格式"""
        mapping = self.field_mapping["unified_to_conversation"]
        
        conversation_state = ConversationState()
        
        # 映射基础字段
        for unified_field, conv_field in mapping.items():
            if unified_field in unified_state:
                conversation_state[conv_field] = unified_state[unified_field]
        
        return conversation_state
    
    async def _to_dict_state(self, unified_state: UnifiedFitnessState) -> Dict[str, Any]:
        """转换为字典格式"""
        return dict(unified_state)
    
    def _convert_messages(self, messages: List) -> List[AnyMessage]:
        """转换消息格式"""
        converted_messages = []

        for msg in messages:
            if isinstance(msg, AnyMessage):
                converted_messages.append(msg)
            elif isinstance(msg, dict):
                if msg.get("role") == "user":
                    converted_messages.append(AnyMessage(role="user", content=msg.get("content", "")))
                elif msg.get("role") == "assistant":
                    converted_messages.append(AnyMessage(role="assistant", content=msg.get("content", "")))
            elif isinstance(msg, str):
                converted_messages.append(AnyMessage(role="user", content=msg))

        return converted_messages
    
    async def _create_default_state(self, conversation_id: str) -> UnifiedFitnessState:
        """创建默认状态"""
        return UnifiedFitnessState(
            conversation_id=conversation_id or "",
            user_id="",
            session_id=conversation_id or "",
            timestamp=datetime.now(),
            
            intent="",
            confidence=0.0,
            intent_parameters={},
            
            user_profile={},
            training_params={},
            fitness_goals=[],
            
            flow_state={},
            current_state_name="idle",
            current_node="",
            processing_system="default",
            
            response_content="",
            response_type="text",
            structured_data={},
            
            error_count=0,
            retry_count=0,
            processing_start_time=time.time(),
            node_execution_times={},
            
            parallel_results=[],
            selected_result=None,
            
            messages=[]
        )
    
    def _get_from_cache(self, key: str) -> Optional[UnifiedFitnessState]:
        """从缓存获取"""
        if key in self.conversion_cache:
            cached_data = self.conversion_cache[key]
            if time.time() - cached_data["timestamp"] < self.cache_ttl:
                return cached_data["state"]
            else:
                del self.conversion_cache[key]
        return None
    
    def _set_to_cache(self, key: str, state: UnifiedFitnessState):
        """设置缓存"""
        self.conversion_cache[key] = {
            "state": state,
            "timestamp": time.time()
        }
