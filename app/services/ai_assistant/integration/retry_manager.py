"""
智能重试管理器

基于错误类型的智能重试策略：
- 指数退避算法
- 熔断器模式集成
- 重试条件智能判断
- 与工作流编排器集成

目标：重试成功率>80%，避免无效重试
"""

import asyncio
import time
import random
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime, timedelta
from enum import Enum

from .error_handler import ErrorCategory, ErrorSeverity
from .interfaces import RetryManagerInterface
from ..langgraph.state_definitions import UnifiedFitnessState
from ...logger.logger import get_logger

logger = get_logger(__name__)


class RetryStrategy(Enum):
    """重试策略"""
    FIXED_DELAY = "fixed_delay"           # 固定延迟
    EXPONENTIAL_BACKOFF = "exponential"   # 指数退避
    LINEAR_BACKOFF = "linear"             # 线性退避
    JITTERED_BACKOFF = "jittered"         # 带抖动的退避
    ADAPTIVE = "adaptive"                 # 自适应策略


class RetryResult(Enum):
    """重试结果"""
    SUCCESS = "success"                   # 重试成功
    FAILED = "failed"                     # 重试失败
    EXHAUSTED = "exhausted"               # 重试次数耗尽
    CIRCUIT_OPEN = "circuit_open"         # 熔断器开启
    NOT_RETRYABLE = "not_retryable"       # 不可重试


class RetryConfig:
    """重试配置"""
    
    def __init__(self,
                 max_retries: int = 3,
                 base_delay: float = 1.0,
                 max_delay: float = 60.0,
                 backoff_factor: float = 2.0,
                 jitter: bool = True,
                 strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        self.jitter = jitter
        self.strategy = strategy


class IntelligentRetryManager(RetryManagerInterface):
    """智能重试管理器"""
    
    def __init__(self, error_handler=None):
        self.error_handler = error_handler
        
        # 重试配置映射
        self.retry_configs = self._init_retry_configs()
        
        # 重试统计
        self.retry_stats = {
            "total_retries": 0,
            "successful_retries": 0,
            "failed_retries": 0,
            "exhausted_retries": 0,
            "retries_by_category": {},
            "retries_by_strategy": {}
        }
        
        # 自适应学习数据
        self.adaptive_data = {}
        
        logger.info("智能重试管理器初始化完成")
    
    def _init_retry_configs(self) -> Dict[ErrorCategory, RetryConfig]:
        """初始化重试配置"""
        return {
            ErrorCategory.LLM: RetryConfig(
                max_retries=3,
                base_delay=1.0,
                max_delay=30.0,
                backoff_factor=2.0,
                strategy=RetryStrategy.EXPONENTIAL_BACKOFF
            ),
            ErrorCategory.DATABASE: RetryConfig(
                max_retries=2,
                base_delay=0.5,
                max_delay=10.0,
                backoff_factor=1.5,
                strategy=RetryStrategy.LINEAR_BACKOFF
            ),
            ErrorCategory.NETWORK: RetryConfig(
                max_retries=3,
                base_delay=1.0,
                max_delay=20.0,
                backoff_factor=2.0,
                strategy=RetryStrategy.JITTERED_BACKOFF
            ),
            ErrorCategory.TIMEOUT: RetryConfig(
                max_retries=2,
                base_delay=2.0,
                max_delay=15.0,
                backoff_factor=1.0,
                strategy=RetryStrategy.FIXED_DELAY
            ),
            ErrorCategory.SYSTEM: RetryConfig(
                max_retries=2,
                base_delay=3.0,
                max_delay=30.0,
                backoff_factor=2.0,
                strategy=RetryStrategy.EXPONENTIAL_BACKOFF
            ),
            ErrorCategory.BUSINESS: RetryConfig(
                max_retries=1,
                base_delay=1.0,
                max_delay=5.0,
                backoff_factor=1.0,
                strategy=RetryStrategy.FIXED_DELAY
            ),
            ErrorCategory.VALIDATION: RetryConfig(
                max_retries=0,  # 验证错误通常不需要重试
                base_delay=0.0,
                max_delay=0.0,
                backoff_factor=1.0,
                strategy=RetryStrategy.FIXED_DELAY
            )
        }
    
    async def should_retry(self, 
                          error: Exception,
                          error_category: ErrorCategory,
                          error_severity: ErrorSeverity,
                          retry_count: int,
                          context: Dict[str, Any]) -> bool:
        """判断是否应该重试"""
        try:
            # 获取重试配置
            config = self.retry_configs.get(error_category, self.retry_configs[ErrorCategory.BUSINESS])
            
            # 检查重试次数限制
            if retry_count >= config.max_retries:
                logger.debug(f"重试次数已达上限: {retry_count}/{config.max_retries}")
                return False
            
            # 检查错误严重性
            if error_severity == ErrorSeverity.CRITICAL:
                logger.debug("严重错误，不进行重试")
                return False
            
            # 检查熔断器状态
            if self.error_handler:
                circuit_breaker = self.error_handler.circuit_breakers.get(error_category)
                if circuit_breaker and circuit_breaker.is_open():
                    logger.debug(f"熔断器开启，不进行重试: {error_category.value}")
                    return False
            
            # 检查特定错误类型
            if not self._is_retryable_error(error, error_category):
                logger.debug(f"错误类型不可重试: {type(error).__name__}")
                return False
            
            # 自适应判断（基于历史成功率）
            if config.strategy == RetryStrategy.ADAPTIVE:
                success_rate = self._get_adaptive_success_rate(error_category, error_severity)
                if success_rate < 0.2:  # 成功率低于20%时停止重试
                    logger.debug(f"自适应判断：成功率过低 ({success_rate:.2%})，停止重试")
                    return False
            
            logger.debug(f"决定重试: {error_category.value}, 第{retry_count + 1}次")
            return True
            
        except Exception as e:
            logger.error(f"重试判断失败: {str(e)}")
            return False
    
    async def calculate_delay(self,
                            error_category: ErrorCategory,
                            retry_count: int,
                            context: Dict[str, Any]) -> float:
        """计算重试延迟"""
        try:
            config = self.retry_configs.get(error_category, self.retry_configs[ErrorCategory.BUSINESS])
            
            if config.strategy == RetryStrategy.FIXED_DELAY:
                delay = config.base_delay
            
            elif config.strategy == RetryStrategy.LINEAR_BACKOFF:
                delay = config.base_delay * (retry_count + 1)
            
            elif config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
                delay = config.base_delay * (config.backoff_factor ** retry_count)
            
            elif config.strategy == RetryStrategy.JITTERED_BACKOFF:
                base_delay = config.base_delay * (config.backoff_factor ** retry_count)
                jitter = random.uniform(0.5, 1.5)  # ±50% 抖动
                delay = base_delay * jitter
            
            elif config.strategy == RetryStrategy.ADAPTIVE:
                delay = self._calculate_adaptive_delay(error_category, retry_count, context)
            
            else:
                delay = config.base_delay
            
            # 限制最大延迟
            delay = min(delay, config.max_delay)
            
            logger.debug(f"计算重试延迟: {error_category.value}, 第{retry_count + 1}次, 延迟: {delay:.2f}s")
            return delay
            
        except Exception as e:
            logger.error(f"计算重试延迟失败: {str(e)}")
            return 1.0  # 默认延迟
    
    async def execute_with_retry(self,
                               func: Callable,
                               error_category: ErrorCategory,
                               context: Dict[str, Any],
                               *args, **kwargs) -> Dict[str, Any]:
        """执行带重试的函数"""
        retry_count = 0
        last_error = None
        start_time = time.time()
        
        config = self.retry_configs.get(error_category, self.retry_configs[ErrorCategory.BUSINESS])
        
        while retry_count <= config.max_retries:
            try:
                # 执行函数
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # 成功执行
                if retry_count > 0:
                    self.retry_stats["successful_retries"] += 1
                    self._update_adaptive_data(error_category, True, retry_count)
                    logger.info(f"重试成功: {error_category.value}, 重试次数: {retry_count}")
                
                return {
                    "success": True,
                    "result": result,
                    "retry_count": retry_count,
                    "total_time": time.time() - start_time
                }
                
            except Exception as e:
                last_error = e
                
                # 分析错误
                if self.error_handler:
                    error_info = await self.error_handler._analyze_error(
                        e, context, context.get("conversation_id", ""), context.get("user_id", "")
                    )
                    error_severity = error_info.severity
                else:
                    error_severity = ErrorSeverity.MEDIUM
                
                # 判断是否应该重试
                should_retry = await self.should_retry(e, error_category, error_severity, retry_count, context)
                
                if not should_retry:
                    break
                
                # 计算延迟并等待
                delay = await self.calculate_delay(error_category, retry_count, context)
                if delay > 0:
                    await asyncio.sleep(delay)
                
                retry_count += 1
                self.retry_stats["total_retries"] += 1
                
                # 更新统计
                category_key = error_category.value
                if category_key not in self.retry_stats["retries_by_category"]:
                    self.retry_stats["retries_by_category"][category_key] = 0
                self.retry_stats["retries_by_category"][category_key] += 1
                
                logger.warning(f"重试执行: {error_category.value}, 第{retry_count}次, 错误: {str(e)}")
        
        # 重试失败
        if retry_count > config.max_retries:
            self.retry_stats["exhausted_retries"] += 1
            result_type = RetryResult.EXHAUSTED
        else:
            self.retry_stats["failed_retries"] += 1
            result_type = RetryResult.FAILED
        
        self._update_adaptive_data(error_category, False, retry_count)
        
        logger.error(f"重试失败: {error_category.value}, 最终错误: {str(last_error)}")
        
        return {
            "success": False,
            "error": last_error,
            "retry_count": retry_count,
            "result_type": result_type.value,
            "total_time": time.time() - start_time
        }
    
    def _is_retryable_error(self, error: Exception, error_category: ErrorCategory) -> bool:
        """判断错误是否可重试"""
        error_type = type(error).__name__.lower()
        error_message = str(error).lower()
        
        # 不可重试的错误类型
        non_retryable_errors = [
            "syntaxerror", "typeerror", "valueerror", "keyerror",
            "attributeerror", "importerror", "modulenotfounderror"
        ]
        
        if any(err in error_type for err in non_retryable_errors):
            return False
        
        # 不可重试的错误消息
        non_retryable_messages = [
            "unauthorized", "forbidden", "not found", "bad request",
            "invalid", "malformed", "syntax error"
        ]
        
        if any(msg in error_message for msg in non_retryable_messages):
            return False
        
        # 验证错误通常不可重试
        if error_category == ErrorCategory.VALIDATION:
            return False
        
        return True
    
    def _calculate_adaptive_delay(self, error_category: ErrorCategory, retry_count: int, context: Dict[str, Any]) -> float:
        """计算自适应延迟"""
        # 基于历史数据调整延迟
        success_rate = self._get_adaptive_success_rate(error_category, ErrorSeverity.MEDIUM)
        
        base_config = self.retry_configs[error_category]
        base_delay = base_config.base_delay * (base_config.backoff_factor ** retry_count)
        
        # 成功率越低，延迟越长
        if success_rate < 0.3:
            multiplier = 2.0
        elif success_rate < 0.6:
            multiplier = 1.5
        else:
            multiplier = 1.0
        
        return min(base_delay * multiplier, base_config.max_delay)
    
    def _get_adaptive_success_rate(self, error_category: ErrorCategory, error_severity: ErrorSeverity) -> float:
        """获取自适应成功率"""
        key = f"{error_category.value}_{error_severity.value}"
        
        if key not in self.adaptive_data:
            return 0.5  # 默认成功率
        
        data = self.adaptive_data[key]
        total_attempts = data["success"] + data["failure"]
        
        if total_attempts == 0:
            return 0.5
        
        return data["success"] / total_attempts
    
    def _update_adaptive_data(self, error_category: ErrorCategory, success: bool, retry_count: int):
        """更新自适应数据"""
        # 简化实现，实际可以根据错误严重性等更细粒度地记录
        key = f"{error_category.value}_medium"
        
        if key not in self.adaptive_data:
            self.adaptive_data[key] = {"success": 0, "failure": 0, "avg_retry_count": 0}
        
        if success:
            self.adaptive_data[key]["success"] += 1
        else:
            self.adaptive_data[key]["failure"] += 1
        
        # 更新平均重试次数
        total_attempts = self.adaptive_data[key]["success"] + self.adaptive_data[key]["failure"]
        current_avg = self.adaptive_data[key]["avg_retry_count"]
        self.adaptive_data[key]["avg_retry_count"] = (current_avg * (total_attempts - 1) + retry_count) / total_attempts
    
    async def get_retry_stats(self) -> Dict[str, Any]:
        """获取重试统计信息"""
        total_retries = self.retry_stats["total_retries"]
        success_rate = (self.retry_stats["successful_retries"] / total_retries) if total_retries > 0 else 0
        
        return {
            "total_retries": total_retries,
            "successful_retries": self.retry_stats["successful_retries"],
            "failed_retries": self.retry_stats["failed_retries"],
            "exhausted_retries": self.retry_stats["exhausted_retries"],
            "success_rate": success_rate,
            "retries_by_category": self.retry_stats["retries_by_category"],
            "adaptive_data": self.adaptive_data
        }
    
    async def reset_retry_stats(self):
        """重置重试统计"""
        self.retry_stats = {
            "total_retries": 0,
            "successful_retries": 0,
            "failed_retries": 0,
            "exhausted_retries": 0,
            "retries_by_category": {},
            "retries_by_strategy": {}
        }
        
        self.adaptive_data = {}
        logger.info("重试统计已重置")
