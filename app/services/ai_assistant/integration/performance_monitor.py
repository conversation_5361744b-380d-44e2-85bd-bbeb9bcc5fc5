"""
性能监控和优化器

实时性能指标收集和分析：
- 系统性能监控
- 自动性能调优
- 资源使用优化
- 性能报告生成

目标：平均响应时间<100ms，系统资源使用率<80%
"""

import asyncio
import time
import psutil
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import deque
from enum import Enum

from .interfaces import PerformanceMonitorInterface
from ...logger.logger import get_logger

logger = get_logger(__name__)


class MetricType(Enum):
    """指标类型"""
    RESPONSE_TIME = "response_time"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"
    CPU_USAGE = "cpu_usage"
    MEMORY_USAGE = "memory_usage"
    CACHE_HIT_RATE = "cache_hit_rate"
    DATABASE_LATENCY = "database_latency"
    LLM_LATENCY = "llm_latency"


@dataclass
class PerformanceMetric:
    """性能指标"""
    metric_type: MetricType
    value: float
    timestamp: datetime
    context: Dict[str, Any] = None
    tags: Dict[str, str] = None


@dataclass
class SystemResource:
    """系统资源"""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    network_io_bytes: int
    timestamp: datetime


class PerformanceCollector:
    """性能数据收集器"""
    
    def __init__(self, max_samples: int = 1000):
        self.max_samples = max_samples
        self.metrics: Dict[MetricType, deque] = {
            metric_type: deque(maxlen=max_samples) 
            for metric_type in MetricType
        }
        self.system_resources = deque(maxlen=max_samples)
        self._lock = threading.Lock()
    
    def add_metric(self, metric: PerformanceMetric):
        """添加性能指标"""
        with self._lock:
            self.metrics[metric.metric_type].append(metric)
    
    def add_system_resource(self, resource: SystemResource):
        """添加系统资源数据"""
        with self._lock:
            self.system_resources.append(resource)
    
    def get_recent_metrics(self, metric_type: MetricType, count: int = 100) -> List[PerformanceMetric]:
        """获取最近的指标"""
        with self._lock:
            metrics = list(self.metrics[metric_type])
            return metrics[-count:] if len(metrics) > count else metrics
    
    def get_recent_resources(self, count: int = 100) -> List[SystemResource]:
        """获取最近的系统资源数据"""
        with self._lock:
            resources = list(self.system_resources)
            return resources[-count:] if len(resources) > count else resources
    
    def calculate_average(self, metric_type: MetricType, time_window_minutes: int = 5) -> float:
        """计算时间窗口内的平均值"""
        with self._lock:
            now = datetime.now()
            cutoff_time = now - timedelta(minutes=time_window_minutes)
            
            recent_metrics = [
                m for m in self.metrics[metric_type] 
                if m.timestamp >= cutoff_time
            ]
            
            if not recent_metrics:
                return 0.0
            
            return sum(m.value for m in recent_metrics) / len(recent_metrics)
    
    def calculate_percentile(self, metric_type: MetricType, percentile: float, time_window_minutes: int = 5) -> float:
        """计算百分位数"""
        with self._lock:
            now = datetime.now()
            cutoff_time = now - timedelta(minutes=time_window_minutes)
            
            recent_metrics = [
                m.value for m in self.metrics[metric_type] 
                if m.timestamp >= cutoff_time
            ]
            
            if not recent_metrics:
                return 0.0
            
            recent_metrics.sort()
            index = int(len(recent_metrics) * percentile / 100)
            return recent_metrics[min(index, len(recent_metrics) - 1)]


class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self, collector: PerformanceCollector):
        self.collector = collector
        self.optimization_rules = self._init_optimization_rules()
        self.optimization_history = []
    
    def _init_optimization_rules(self) -> List[Dict[str, Any]]:
        """初始化优化规则"""
        return [
            {
                "name": "high_response_time",
                "condition": lambda: self.collector.calculate_average(MetricType.RESPONSE_TIME) > 100,
                "action": self._optimize_response_time,
                "priority": 1
            },
            {
                "name": "high_cpu_usage",
                "condition": lambda: self._get_current_cpu_usage() > 80,
                "action": self._optimize_cpu_usage,
                "priority": 2
            },
            {
                "name": "high_memory_usage",
                "condition": lambda: self._get_current_memory_usage() > 80,
                "action": self._optimize_memory_usage,
                "priority": 2
            },
            {
                "name": "low_cache_hit_rate",
                "condition": lambda: self.collector.calculate_average(MetricType.CACHE_HIT_RATE) < 70,
                "action": self._optimize_cache_performance,
                "priority": 3
            },
            {
                "name": "high_error_rate",
                "condition": lambda: self.collector.calculate_average(MetricType.ERROR_RATE) > 5,
                "action": self._optimize_error_handling,
                "priority": 1
            }
        ]
    
    async def run_optimization(self) -> List[Dict[str, Any]]:
        """运行性能优化"""
        optimizations_applied = []
        
        # 按优先级排序规则
        sorted_rules = sorted(self.optimization_rules, key=lambda x: x["priority"])
        
        for rule in sorted_rules:
            try:
                if rule["condition"]():
                    logger.info(f"触发优化规则: {rule['name']}")
                    
                    optimization_result = await rule["action"]()
                    optimization_result["rule_name"] = rule["name"]
                    optimization_result["timestamp"] = datetime.now()
                    
                    optimizations_applied.append(optimization_result)
                    self.optimization_history.append(optimization_result)
                    
                    # 限制历史记录数量
                    if len(self.optimization_history) > 100:
                        self.optimization_history = self.optimization_history[-100:]
            
            except Exception as e:
                logger.error(f"优化规则执行失败: {rule['name']}, 错误: {str(e)}")
        
        return optimizations_applied
    
    async def _optimize_response_time(self) -> Dict[str, Any]:
        """优化响应时间"""
        # 分析响应时间瓶颈
        avg_response_time = self.collector.calculate_average(MetricType.RESPONSE_TIME)
        p95_response_time = self.collector.calculate_percentile(MetricType.RESPONSE_TIME, 95)
        
        optimizations = []
        
        # 如果P95响应时间过高，建议增加缓存
        if p95_response_time > 200:
            optimizations.append("增加缓存预热")
        
        # 如果平均响应时间过高，建议优化数据库查询
        if avg_response_time > 150:
            optimizations.append("优化数据库查询")
        
        # 如果LLM延迟过高，建议使用更快的模型
        avg_llm_latency = self.collector.calculate_average(MetricType.LLM_LATENCY)
        if avg_llm_latency > 2000:  # 2秒
            optimizations.append("切换到更快的LLM模型")
        
        return {
            "type": "response_time_optimization",
            "current_avg": avg_response_time,
            "current_p95": p95_response_time,
            "optimizations": optimizations,
            "success": True
        }
    
    async def _optimize_cpu_usage(self) -> Dict[str, Any]:
        """优化CPU使用率"""
        current_cpu = self._get_current_cpu_usage()
        
        optimizations = []
        
        if current_cpu > 90:
            optimizations.append("启用请求限流")
            optimizations.append("增加异步处理")
        elif current_cpu > 80:
            optimizations.append("优化计算密集型操作")
            optimizations.append("启用结果缓存")
        
        return {
            "type": "cpu_optimization",
            "current_usage": current_cpu,
            "optimizations": optimizations,
            "success": True
        }
    
    async def _optimize_memory_usage(self) -> Dict[str, Any]:
        """优化内存使用率"""
        current_memory = self._get_current_memory_usage()
        
        optimizations = []
        
        if current_memory > 90:
            optimizations.append("清理过期缓存")
            optimizations.append("减少内存缓存大小")
        elif current_memory > 80:
            optimizations.append("优化数据结构")
            optimizations.append("启用内存压缩")
        
        return {
            "type": "memory_optimization",
            "current_usage": current_memory,
            "optimizations": optimizations,
            "success": True
        }
    
    async def _optimize_cache_performance(self) -> Dict[str, Any]:
        """优化缓存性能"""
        cache_hit_rate = self.collector.calculate_average(MetricType.CACHE_HIT_RATE)
        
        optimizations = []
        
        if cache_hit_rate < 50:
            optimizations.append("增加缓存预热策略")
            optimizations.append("调整缓存TTL")
        elif cache_hit_rate < 70:
            optimizations.append("优化缓存键设计")
            optimizations.append("增加热点数据缓存")
        
        return {
            "type": "cache_optimization",
            "current_hit_rate": cache_hit_rate,
            "optimizations": optimizations,
            "success": True
        }
    
    async def _optimize_error_handling(self) -> Dict[str, Any]:
        """优化错误处理"""
        error_rate = self.collector.calculate_average(MetricType.ERROR_RATE)
        
        optimizations = []
        
        if error_rate > 10:
            optimizations.append("启用熔断器")
            optimizations.append("增加重试机制")
        elif error_rate > 5:
            optimizations.append("优化错误恢复策略")
            optimizations.append("增加降级处理")
        
        return {
            "type": "error_handling_optimization",
            "current_error_rate": error_rate,
            "optimizations": optimizations,
            "success": True
        }
    
    def _get_current_cpu_usage(self) -> float:
        """获取当前CPU使用率"""
        try:
            return psutil.cpu_percent(interval=1)
        except Exception:
            return 0.0
    
    def _get_current_memory_usage(self) -> float:
        """获取当前内存使用率"""
        try:
            return psutil.virtual_memory().percent
        except Exception:
            return 0.0


class IntelligentPerformanceMonitor(PerformanceMonitorInterface):
    """智能性能监控器"""
    
    def __init__(self, error_handler=None, cache_manager=None, retry_manager=None):
        self.error_handler = error_handler
        self.cache_manager = cache_manager
        self.retry_manager = retry_manager
        
        # 性能数据收集器
        self.collector = PerformanceCollector()
        
        # 性能优化器
        self.optimizer = PerformanceOptimizer(self.collector)
        
        # 监控配置
        self.monitoring_enabled = True
        self.auto_optimization_enabled = True
        self.collection_interval = 10  # 秒
        self.optimization_interval = 60  # 秒
        
        # 监控任务
        self._monitoring_task = None
        self._optimization_task = None
        
        # 性能阈值
        self.performance_thresholds = {
            MetricType.RESPONSE_TIME: 100,  # ms
            MetricType.ERROR_RATE: 5,       # %
            MetricType.CPU_USAGE: 80,       # %
            MetricType.MEMORY_USAGE: 80,    # %
            MetricType.CACHE_HIT_RATE: 90   # %
        }
        
        logger.info("智能性能监控器初始化完成")
    
    async def start_monitoring(self):
        """启动性能监控"""
        if self.monitoring_enabled and not self._monitoring_task:
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            logger.info("性能监控已启动")
        
        if self.auto_optimization_enabled and not self._optimization_task:
            self._optimization_task = asyncio.create_task(self._optimization_loop())
            logger.info("自动优化已启动")
    
    async def stop_monitoring(self):
        """停止性能监控"""
        if self._monitoring_task:
            self._monitoring_task.cancel()
            self._monitoring_task = None
            logger.info("性能监控已停止")
        
        if self._optimization_task:
            self._optimization_task.cancel()
            self._optimization_task = None
            logger.info("自动优化已停止")
    
    async def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_enabled:
            try:
                await self._collect_system_metrics()
                await asyncio.sleep(self.collection_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"性能监控循环错误: {str(e)}")
                await asyncio.sleep(self.collection_interval)
    
    async def _optimization_loop(self):
        """优化循环"""
        while self.auto_optimization_enabled:
            try:
                await self.optimizer.run_optimization()
                await asyncio.sleep(self.optimization_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"自动优化循环错误: {str(e)}")
                await asyncio.sleep(self.optimization_interval)
    
    async def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # 收集系统资源
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            network = psutil.net_io_counters()
            
            system_resource = SystemResource(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                memory_available_mb=memory.available / 1024 / 1024,
                disk_usage_percent=disk.percent,
                network_io_bytes=network.bytes_sent + network.bytes_recv,
                timestamp=datetime.now()
            )
            
            self.collector.add_system_resource(system_resource)
            
            # 添加系统指标
            self.record_metric(MetricType.CPU_USAGE, cpu_percent)
            self.record_metric(MetricType.MEMORY_USAGE, memory.percent)
            
            # 收集应用指标
            await self._collect_application_metrics()
            
        except Exception as e:
            logger.error(f"系统指标收集失败: {str(e)}")
    
    async def _collect_application_metrics(self):
        """收集应用指标"""
        try:
            # 收集缓存指标
            if self.cache_manager:
                cache_stats = await self.cache_manager.get_cache_stats()
                if cache_stats and "overall" in cache_stats:
                    hit_rate = cache_stats["overall"]["hit_rate"] * 100
                    self.record_metric(MetricType.CACHE_HIT_RATE, hit_rate)
            
            # 收集错误处理指标
            if self.error_handler:
                error_stats = await self.error_handler.get_error_stats()
                if error_stats:
                    total_errors = error_stats.get("total_errors", 0)
                    recovery_rate = error_stats.get("recovery_rate", 0)
                    error_rate = (1 - recovery_rate) * 100 if recovery_rate > 0 else 0
                    self.record_metric(MetricType.ERROR_RATE, error_rate)
            
            # 收集重试指标
            if self.retry_manager:
                retry_stats = await self.retry_manager.get_retry_stats()
                if retry_stats:
                    success_rate = retry_stats.get("success_rate", 0)
                    # 重试成功率可以作为系统稳定性指标
                    self.record_metric(MetricType.THROUGHPUT, success_rate * 100)
        
        except Exception as e:
            logger.error(f"应用指标收集失败: {str(e)}")
    
    def record_metric(self, metric_type: MetricType, value: float, context: Dict[str, Any] = None, tags: Dict[str, str] = None):
        """记录性能指标"""
        try:
            metric = PerformanceMetric(
                metric_type=metric_type,
                value=value,
                timestamp=datetime.now(),
                context=context or {},
                tags=tags or {}
            )
            
            self.collector.add_metric(metric)
            
            # 检查阈值告警
            threshold = self.performance_thresholds.get(metric_type)
            if threshold and value > threshold:
                logger.warning(f"性能指标超过阈值: {metric_type.value} = {value}, 阈值: {threshold}")
        
        except Exception as e:
            logger.error(f"记录性能指标失败: {str(e)}")
    
    async def get_performance_report(self, time_window_minutes: int = 60) -> Dict[str, Any]:
        """获取性能报告"""
        try:
            report = {
                "time_window_minutes": time_window_minutes,
                "generated_at": datetime.now().isoformat(),
                "metrics": {},
                "system_resources": {},
                "optimization_history": self.optimizer.optimization_history[-10:],  # 最近10次优化
                "alerts": []
            }
            
            # 收集各类指标的统计信息
            for metric_type in MetricType:
                avg_value = self.collector.calculate_average(metric_type, time_window_minutes)
                p95_value = self.collector.calculate_percentile(metric_type, 95, time_window_minutes)
                p99_value = self.collector.calculate_percentile(metric_type, 99, time_window_minutes)
                
                report["metrics"][metric_type.value] = {
                    "average": avg_value,
                    "p95": p95_value,
                    "p99": p99_value,
                    "threshold": self.performance_thresholds.get(metric_type, 0),
                    "status": "normal" if avg_value <= self.performance_thresholds.get(metric_type, float('inf')) else "warning"
                }
                
                # 检查告警
                threshold = self.performance_thresholds.get(metric_type)
                if threshold and avg_value > threshold:
                    report["alerts"].append({
                        "metric": metric_type.value,
                        "current_value": avg_value,
                        "threshold": threshold,
                        "severity": "high" if avg_value > threshold * 1.5 else "medium"
                    })
            
            # 收集系统资源统计
            recent_resources = self.collector.get_recent_resources(time_window_minutes * 6)  # 假设每10秒收集一次
            if recent_resources:
                avg_cpu = sum(r.cpu_percent for r in recent_resources) / len(recent_resources)
                avg_memory = sum(r.memory_percent for r in recent_resources) / len(recent_resources)
                avg_disk = sum(r.disk_usage_percent for r in recent_resources) / len(recent_resources)
                
                report["system_resources"] = {
                    "cpu_usage_avg": avg_cpu,
                    "memory_usage_avg": avg_memory,
                    "disk_usage_avg": avg_disk,
                    "samples_count": len(recent_resources)
                }
            
            return report
            
        except Exception as e:
            logger.error(f"生成性能报告失败: {str(e)}")
            return {"error": str(e)}
