"""
统一错误处理器

企业级错误处理体系，整合现有错误处理逻辑：
- 错误分类和严重性评估
- 智能错误处理策略
- 熔断器和重试机制
- 错误统计和监控

目标：错误恢复率>95%，系统可用性>99.9%
"""

import asyncio
import traceback
import time
import uuid
from enum import Enum
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass

from .interfaces import ErrorHandlerInterface
from ..langgraph.state_definitions import UnifiedFitnessState
from ...logger.logger import get_logger

logger = get_logger(__name__)


class ErrorSeverity(Enum):
    """错误严重级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """错误分类"""
    SYSTEM = "system"           # 系统错误
    LLM = "llm"                # LLM服务错误
    DATABASE = "database"       # 数据库错误
    NETWORK = "network"         # 网络错误
    VALIDATION = "validation"   # 参数验证错误
    BUSINESS = "business"       # 业务逻辑错误
    TIMEOUT = "timeout"         # 超时错误


@dataclass
class ErrorInfo:
    """错误信息"""
    error_id: str
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    details: Dict[str, Any]
    timestamp: datetime
    conversation_id: str
    user_id: str
    stack_trace: str
    context: Dict[str, Any]


class CircuitBreaker:
    """熔断器实现"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def is_open(self) -> bool:
        """检查熔断器是否开启"""
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "HALF_OPEN"
                return False
            return True
        return False
    
    def record_success(self):
        """记录成功"""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def record_failure(self):
        """记录失败"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"


class UnifiedErrorHandler(ErrorHandlerInterface):
    """统一错误处理器 - 企业级错误处理体系"""
    
    def __init__(self, db_session, redis_client=None):
        self.db = db_session
        self.redis = redis_client
        
        # 错误处理策略映射
        self.error_strategies = self._init_error_strategies()
        
        # 错误统计
        self.error_stats = {
            "total_errors": 0,
            "errors_by_category": {},
            "errors_by_severity": {},
            "recovery_success": 0,
            "recovery_failure": 0
        }
        
        # 熔断器配置
        self.circuit_breakers = {
            ErrorCategory.LLM: CircuitBreaker(failure_threshold=3, recovery_timeout=30),
            ErrorCategory.DATABASE: CircuitBreaker(failure_threshold=2, recovery_timeout=60),
            ErrorCategory.NETWORK: CircuitBreaker(failure_threshold=5, recovery_timeout=30),
        }
        
        # 重试配置
        self.retry_config = {
            ErrorCategory.LLM: {"max_retries": 3, "backoff_factor": 2.0, "base_delay": 1.0},
            ErrorCategory.DATABASE: {"max_retries": 2, "backoff_factor": 1.5, "base_delay": 0.5},
            ErrorCategory.NETWORK: {"max_retries": 3, "backoff_factor": 2.0, "base_delay": 1.0},
            ErrorCategory.TIMEOUT: {"max_retries": 2, "backoff_factor": 1.0, "base_delay": 0.5},
        }
        
        logger.info("统一错误处理器初始化完成")
    
    def _init_error_strategies(self) -> Dict[ErrorCategory, Dict[ErrorSeverity, Callable]]:
        """初始化错误处理策略"""
        return {
            ErrorCategory.LLM: {
                ErrorSeverity.LOW: self._handle_llm_low_severity,
                ErrorSeverity.MEDIUM: self._handle_llm_medium_severity,
                ErrorSeverity.HIGH: self._handle_llm_high_severity,
                ErrorSeverity.CRITICAL: self._handle_llm_critical_severity,
            },
            ErrorCategory.DATABASE: {
                ErrorSeverity.LOW: self._handle_db_low_severity,
                ErrorSeverity.MEDIUM: self._handle_db_medium_severity,
                ErrorSeverity.HIGH: self._handle_db_high_severity,
                ErrorSeverity.CRITICAL: self._handle_db_critical_severity,
            },
            ErrorCategory.NETWORK: {
                ErrorSeverity.LOW: self._handle_network_low_severity,
                ErrorSeverity.MEDIUM: self._handle_network_medium_severity,
                ErrorSeverity.HIGH: self._handle_network_high_severity,
                ErrorSeverity.CRITICAL: self._handle_network_critical_severity,
            },
            ErrorCategory.VALIDATION: {
                ErrorSeverity.LOW: self._handle_validation_error,
                ErrorSeverity.MEDIUM: self._handle_validation_error,
                ErrorSeverity.HIGH: self._handle_validation_error,
                ErrorSeverity.CRITICAL: self._handle_validation_error,
            },
            ErrorCategory.BUSINESS: {
                ErrorSeverity.LOW: self._handle_business_low_severity,
                ErrorSeverity.MEDIUM: self._handle_business_medium_severity,
                ErrorSeverity.HIGH: self._handle_business_high_severity,
                ErrorSeverity.CRITICAL: self._handle_business_critical_severity,
            },
            ErrorCategory.TIMEOUT: {
                ErrorSeverity.LOW: self._handle_timeout_error,
                ErrorSeverity.MEDIUM: self._handle_timeout_error,
                ErrorSeverity.HIGH: self._handle_timeout_error,
                ErrorSeverity.CRITICAL: self._handle_timeout_error,
            },
            ErrorCategory.SYSTEM: {
                ErrorSeverity.LOW: self._handle_system_low_severity,
                ErrorSeverity.MEDIUM: self._handle_system_medium_severity,
                ErrorSeverity.HIGH: self._handle_system_high_severity,
                ErrorSeverity.CRITICAL: self._handle_system_critical_severity,
            }
        }
    
    async def handle_error(self, 
                          error: Exception,
                          context: Dict[str, Any],
                          conversation_id: str = "",
                          user_id: str = "") -> Dict[str, Any]:
        """统一错误处理入口"""
        start_time = time.time()
        
        try:
            # 1. 分析错误类型和严重性
            error_info = await self._analyze_error(error, context, conversation_id, user_id)
            
            # 2. 记录错误
            await self._log_error(error_info)
            
            # 3. 更新错误统计
            await self._update_error_stats(error_info)
            
            # 4. 检查熔断器状态
            circuit_breaker = self.circuit_breakers.get(error_info.category)
            if circuit_breaker and circuit_breaker.is_open():
                return await self._handle_circuit_open(error_info)
            
            # 5. 执行错误处理策略
            strategy = self.error_strategies.get(error_info.category, {}).get(error_info.severity)
            if strategy:
                result = await strategy(error_info)
            else:
                result = await self._handle_default_error(error_info)
            
            # 6. 更新熔断器状态
            if circuit_breaker:
                if result.get("success", False):
                    circuit_breaker.record_success()
                    self.error_stats["recovery_success"] += 1
                else:
                    circuit_breaker.record_failure()
                    self.error_stats["recovery_failure"] += 1
            
            # 7. 记录处理时间
            processing_time = time.time() - start_time
            result["processing_time"] = processing_time
            
            logger.info(f"错误处理完成: {error_info.error_id}, 耗时: {processing_time:.3f}s")
            return result
            
        except Exception as e:
            logger.critical(f"错误处理器自身出现错误: {str(e)}")
            return {
                "success": False,
                "error_message": "系统遇到严重错误，请联系技术支持",
                "should_retry": False,
                "recovery_action": "escalate_to_human",
                "error_id": f"handler_error_{int(time.time())}"
            }
    
    async def _analyze_error(self, 
                           error: Exception,
                           context: Dict[str, Any],
                           conversation_id: str,
                           user_id: str) -> ErrorInfo:
        """分析错误类型和严重性"""
        
        error_message = str(error)
        error_type = type(error).__name__
        
        # 错误分类
        category = self._classify_error(error, error_message)
        
        # 严重性评估
        severity = self._assess_severity(error, category, context)
        
        # 生成错误ID
        error_id = f"{category.value}_{int(time.time())}_{str(uuid.uuid4())[:8]}"
        
        return ErrorInfo(
            error_id=error_id,
            category=category,
            severity=severity,
            message=error_message,
            details={
                "error_type": error_type,
                "error_args": getattr(error, 'args', []),
                "context_summary": self._summarize_context(context)
            },
            timestamp=datetime.now(),
            conversation_id=conversation_id,
            user_id=user_id,
            stack_trace=traceback.format_exc(),
            context=context
        )
    
    def _classify_error(self, error: Exception, error_message: str) -> ErrorCategory:
        """错误分类"""
        error_type = type(error).__name__.lower()
        error_msg_lower = error_message.lower()
        
        # LLM相关错误
        if any(keyword in error_msg_lower for keyword in 
               ["api", "token", "rate limit", "quota", "model", "llm", "openai", "qwen"]):
            return ErrorCategory.LLM
        
        # 数据库相关错误
        if any(keyword in error_type for keyword in
               ["sql", "database", "connection", "transaction", "integrity"]):
            return ErrorCategory.DATABASE
        
        # 网络相关错误
        if any(keyword in error_type for keyword in
               ["connection", "timeout", "network", "request", "response", "http"]):
            return ErrorCategory.NETWORK
        
        # 验证相关错误
        if any(keyword in error_type for keyword in
               ["validation", "value", "type", "format", "schema"]):
            return ErrorCategory.VALIDATION
        
        # 超时错误
        if "timeout" in error_msg_lower or "timeout" in error_type:
            return ErrorCategory.TIMEOUT
        
        # 系统错误
        if any(keyword in error_type for keyword in
               ["system", "memory", "permission", "file", "io"]):
            return ErrorCategory.SYSTEM
        
        # 默认为业务逻辑错误
        return ErrorCategory.BUSINESS
    
    def _assess_severity(self, 
                        error: Exception,
                        category: ErrorCategory,
                        context: Dict[str, Any]) -> ErrorSeverity:
        """评估错误严重性"""
        
        error_message = str(error).lower()
        error_type = type(error).__name__.lower()
        
        # 关键词映射严重性
        critical_keywords = ["critical", "fatal", "corruption", "security", "unauthorized"]
        high_keywords = ["failed", "error", "exception", "unable", "denied"]
        medium_keywords = ["warning", "retry", "timeout", "limit"]
        
        # 根据错误类型调整严重性
        if category == ErrorCategory.DATABASE and any(keyword in error_message for keyword in ["integrity", "constraint"]):
            return ErrorSeverity.HIGH
        
        if category == ErrorCategory.LLM and "rate limit" in error_message:
            return ErrorSeverity.MEDIUM
        
        # 基于关键词判断
        if any(keyword in error_message for keyword in critical_keywords):
            return ErrorSeverity.CRITICAL
        elif any(keyword in error_message for keyword in high_keywords):
            return ErrorSeverity.HIGH
        elif any(keyword in error_message for keyword in medium_keywords):
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    def _summarize_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """总结上下文信息"""
        summary = {}
        
        # 提取关键信息
        if "conversation_id" in context:
            summary["conversation_id"] = context["conversation_id"]
        if "user_id" in context:
            summary["user_id"] = context["user_id"]
        if "intent" in context:
            summary["intent"] = context["intent"]
        if "processing_system" in context:
            summary["processing_system"] = context["processing_system"]
        
        # 限制上下文大小
        summary["context_size"] = len(str(context))
        
        return summary

    async def _log_error(self, error_info: ErrorInfo):
        """记录错误信息"""
        try:
            # 记录到日志
            logger.error(
                f"错误处理: {error_info.error_id} | "
                f"类别: {error_info.category.value} | "
                f"严重性: {error_info.severity.value} | "
                f"消息: {error_info.message}"
            )

            # 记录到Redis（如果可用）
            if self.redis:
                error_data = {
                    "error_id": error_info.error_id,
                    "category": error_info.category.value,
                    "severity": error_info.severity.value,
                    "message": error_info.message,
                    "timestamp": error_info.timestamp.isoformat(),
                    "conversation_id": error_info.conversation_id,
                    "user_id": error_info.user_id
                }

                # 存储错误信息（TTL 24小时）
                await self.redis.setex(
                    f"error:{error_info.error_id}",
                    86400,
                    str(error_data)
                )

                # 添加到错误列表（用于统计）
                await self.redis.lpush("error_list", error_info.error_id)
                await self.redis.ltrim("error_list", 0, 999)  # 保留最近1000个错误

        except Exception as e:
            logger.warning(f"错误记录失败: {str(e)}")

    async def _update_error_stats(self, error_info: ErrorInfo):
        """更新错误统计"""
        try:
            self.error_stats["total_errors"] += 1

            # 按类别统计
            category_key = error_info.category.value
            if category_key not in self.error_stats["errors_by_category"]:
                self.error_stats["errors_by_category"][category_key] = 0
            self.error_stats["errors_by_category"][category_key] += 1

            # 按严重性统计
            severity_key = error_info.severity.value
            if severity_key not in self.error_stats["errors_by_severity"]:
                self.error_stats["errors_by_severity"][severity_key] = 0
            self.error_stats["errors_by_severity"][severity_key] += 1

        except Exception as e:
            logger.warning(f"错误统计更新失败: {str(e)}")

    async def _handle_circuit_open(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理熔断器开启状态"""
        logger.warning(f"熔断器开启: {error_info.category.value}")

        return {
            "success": False,
            "error_message": "服务暂时不可用，请稍后重试",
            "should_retry": True,
            "retry_delay": 60,
            "recovery_action": "circuit_breaker_open",
            "error_id": error_info.error_id
        }

    # LLM错误处理策略
    async def _handle_llm_low_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理LLM低严重性错误"""
        return {
            "success": False,
            "error_message": "AI服务暂时不稳定，正在重试...",
            "should_retry": True,
            "retry_delay": 2,
            "recovery_action": "use_fallback_response",
            "error_id": error_info.error_id
        }

    async def _handle_llm_medium_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理LLM中等严重性错误"""
        return {
            "success": False,
            "error_message": "AI服务遇到问题，正在尝试其他方案...",
            "should_retry": True,
            "retry_delay": 5,
            "recovery_action": "switch_llm_provider",
            "error_id": error_info.error_id
        }

    async def _handle_llm_high_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理LLM高严重性错误"""
        return {
            "success": False,
            "error_message": "AI服务遇到严重问题，已转人工处理",
            "should_retry": False,
            "recovery_action": "escalate_to_human",
            "error_id": error_info.error_id
        }

    async def _handle_llm_critical_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理LLM严重错误"""
        await self._notify_ops_team(error_info)

        return {
            "success": False,
            "error_message": "AI服务严重故障，技术团队已收到通知",
            "should_retry": False,
            "recovery_action": "system_maintenance_mode",
            "error_id": error_info.error_id
        }

    # 数据库错误处理策略
    async def _handle_db_low_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理数据库低严重性错误"""
        return {
            "success": False,
            "error_message": "数据访问暂时异常，正在重试...",
            "should_retry": True,
            "retry_delay": 1,
            "recovery_action": "retry_with_backoff",
            "error_id": error_info.error_id
        }

    async def _handle_db_medium_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理数据库中等严重性错误"""
        return {
            "success": False,
            "error_message": "数据库连接不稳定，正在尝试重新连接...",
            "should_retry": True,
            "retry_delay": 3,
            "recovery_action": "reconnect_database",
            "error_id": error_info.error_id
        }

    async def _handle_db_high_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理数据库高严重性错误"""
        return {
            "success": False,
            "error_message": "数据库服务异常，正在切换到备用数据源...",
            "should_retry": True,
            "retry_delay": 10,
            "recovery_action": "switch_to_backup_db",
            "error_id": error_info.error_id
        }

    async def _handle_db_critical_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理数据库严重错误"""
        await self._notify_ops_team(error_info)

        return {
            "success": False,
            "error_message": "数据库服务严重故障，系统进入维护模式",
            "should_retry": True,
            "retry_delay": 60,
            "recovery_action": "system_maintenance_mode",
            "error_id": error_info.error_id
        }

    # 网络错误处理策略
    async def _handle_network_low_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理网络低严重性错误"""
        return {
            "success": False,
            "error_message": "网络连接不稳定，正在重试...",
            "should_retry": True,
            "retry_delay": 2,
            "recovery_action": "retry_request",
            "error_id": error_info.error_id
        }

    async def _handle_network_medium_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理网络中等严重性错误"""
        return {
            "success": False,
            "error_message": "网络服务异常，正在尝试备用线路...",
            "should_retry": True,
            "retry_delay": 5,
            "recovery_action": "switch_network_route",
            "error_id": error_info.error_id
        }

    async def _handle_network_high_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理网络高严重性错误"""
        return {
            "success": False,
            "error_message": "网络服务严重异常，已启用离线模式",
            "should_retry": False,
            "recovery_action": "enable_offline_mode",
            "error_id": error_info.error_id
        }

    async def _handle_network_critical_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理网络严重错误"""
        await self._notify_ops_team(error_info)

        return {
            "success": False,
            "error_message": "网络基础设施故障，技术团队正在处理",
            "should_retry": False,
            "recovery_action": "infrastructure_maintenance",
            "error_id": error_info.error_id
        }

    # 其他错误处理策略
    async def _handle_validation_error(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理验证错误"""
        return {
            "success": False,
            "error_message": "输入参数格式不正确，请检查后重试",
            "should_retry": False,
            "recovery_action": "request_parameter_correction",
            "error_id": error_info.error_id
        }

    async def _handle_timeout_error(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理超时错误"""
        return {
            "success": False,
            "error_message": "请求处理超时，正在重试...",
            "should_retry": True,
            "retry_delay": 3,
            "recovery_action": "retry_with_timeout_extension",
            "error_id": error_info.error_id
        }

    async def _handle_business_low_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理业务逻辑低严重性错误"""
        return {
            "success": False,
            "error_message": "业务处理遇到小问题，正在调整...",
            "should_retry": True,
            "retry_delay": 1,
            "recovery_action": "adjust_business_logic",
            "error_id": error_info.error_id
        }

    async def _handle_business_medium_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理业务逻辑中等严重性错误"""
        return {
            "success": False,
            "error_message": "业务流程异常，正在尝试备用方案...",
            "should_retry": True,
            "retry_delay": 3,
            "recovery_action": "use_fallback_business_logic",
            "error_id": error_info.error_id
        }

    async def _handle_business_high_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理业务逻辑高严重性错误"""
        return {
            "success": False,
            "error_message": "业务逻辑严重异常，已转人工处理",
            "should_retry": False,
            "recovery_action": "escalate_to_business_expert",
            "error_id": error_info.error_id
        }

    async def _handle_business_critical_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理业务逻辑严重错误"""
        await self._notify_ops_team(error_info)

        return {
            "success": False,
            "error_message": "核心业务逻辑故障，技术团队正在紧急处理",
            "should_retry": False,
            "recovery_action": "emergency_business_maintenance",
            "error_id": error_info.error_id
        }

    async def _handle_system_low_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理系统低严重性错误"""
        return {
            "success": False,
            "error_message": "系统资源暂时紧张，正在优化...",
            "should_retry": True,
            "retry_delay": 2,
            "recovery_action": "optimize_system_resources",
            "error_id": error_info.error_id
        }

    async def _handle_system_medium_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理系统中等严重性错误"""
        return {
            "success": False,
            "error_message": "系统服务异常，正在重启相关服务...",
            "should_retry": True,
            "retry_delay": 10,
            "recovery_action": "restart_system_services",
            "error_id": error_info.error_id
        }

    async def _handle_system_high_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理系统高严重性错误"""
        return {
            "success": False,
            "error_message": "系统核心服务异常，正在切换到备用系统...",
            "should_retry": True,
            "retry_delay": 30,
            "recovery_action": "switch_to_backup_system",
            "error_id": error_info.error_id
        }

    async def _handle_system_critical_severity(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """处理系统严重错误"""
        await self._notify_ops_team(error_info)

        return {
            "success": False,
            "error_message": "系统基础设施严重故障，技术团队正在紧急处理",
            "should_retry": False,
            "recovery_action": "emergency_system_maintenance",
            "error_id": error_info.error_id
        }

    async def _handle_default_error(self, error_info: ErrorInfo) -> Dict[str, Any]:
        """默认错误处理"""
        return {
            "success": False,
            "error_message": "系统遇到未知问题，正在分析处理...",
            "should_retry": True,
            "retry_delay": 5,
            "recovery_action": "analyze_and_retry",
            "error_id": error_info.error_id
        }

    async def _notify_ops_team(self, error_info: ErrorInfo):
        """通知运维团队"""
        try:
            # 这里可以集成实际的通知系统（如邮件、短信、钉钉等）
            logger.critical(
                f"严重错误通知 - 错误ID: {error_info.error_id} | "
                f"类别: {error_info.category.value} | "
                f"严重性: {error_info.severity.value} | "
                f"消息: {error_info.message}"
            )

            # 记录到Redis用于监控系统
            if self.redis:
                notification_data = {
                    "error_id": error_info.error_id,
                    "category": error_info.category.value,
                    "severity": error_info.severity.value,
                    "message": error_info.message,
                    "timestamp": error_info.timestamp.isoformat(),
                    "conversation_id": error_info.conversation_id,
                    "user_id": error_info.user_id,
                    "notification_sent": True
                }

                await self.redis.lpush("critical_errors", str(notification_data))
                await self.redis.ltrim("critical_errors", 0, 99)  # 保留最近100个严重错误

        except Exception as e:
            logger.error(f"通知运维团队失败: {str(e)}")

    async def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        total_errors = self.error_stats["total_errors"]
        recovery_attempts = self.error_stats["recovery_success"] + self.error_stats["recovery_failure"]
        recovery_rate = (self.error_stats["recovery_success"] / recovery_attempts) if recovery_attempts > 0 else 0

        return {
            "total_errors": total_errors,
            "errors_by_category": self.error_stats["errors_by_category"],
            "errors_by_severity": self.error_stats["errors_by_severity"],
            "recovery_success": self.error_stats["recovery_success"],
            "recovery_failure": self.error_stats["recovery_failure"],
            "recovery_rate": recovery_rate,
            "circuit_breaker_status": {
                category.value: {
                    "state": breaker.state,
                    "failure_count": breaker.failure_count,
                    "last_failure_time": breaker.last_failure_time
                }
                for category, breaker in self.circuit_breakers.items()
            }
        }

    async def reset_error_stats(self):
        """重置错误统计"""
        self.error_stats = {
            "total_errors": 0,
            "errors_by_category": {},
            "errors_by_severity": {},
            "recovery_success": 0,
            "recovery_failure": 0
        }

        # 重置熔断器
        for breaker in self.circuit_breakers.values():
            breaker.failure_count = 0
            breaker.state = "CLOSED"
            breaker.last_failure_time = None

        logger.info("错误统计已重置")

    async def get_recent_errors(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的错误记录"""
        try:
            if not self.redis:
                return []

            error_ids = await self.redis.lrange("error_list", 0, limit - 1)
            errors = []

            for error_id in error_ids:
                error_data = await self.redis.get(f"error:{error_id}")
                if error_data:
                    errors.append(eval(error_data))  # 注意：生产环境应使用json.loads

            return errors

        except Exception as e:
            logger.error(f"获取最近错误记录失败: {str(e)}")
            return []
