"""
流式处理管理器

基于现有的WebSocket实现和LangGraph流式API，提供统一的流式响应处理：
- 整合现有的WebSocket处理逻辑
- 基于LangGraph的流式API实现
- 支持流式响应中断和恢复
- 实现流式响应延迟<500ms的目标

复用现有的WebSocket基础设施和LangGraph流式能力。
"""

import asyncio
import json
import time
from typing import Dict, Any, AsyncGenerator, Optional, List
from datetime import datetime

from .interfaces import StreamingProcessorInterface
from .state_adapter import IntegratedStateAdapter
from ..langgraph.state_definitions import UnifiedFitnessState

# 导入现有的LangGraph服务
from ...langgraph_service import LangGraphService

from ...logger.logger import get_logger

logger = get_logger(__name__)


class StreamingProcessor(StreamingProcessorInterface):
    """流式处理管理器 - 基于现有WebSocket和LangGraph流式实现"""
    
    def __init__(self, db_session, llm_service):
        self.db_session = db_session
        self.llm_service = llm_service
        
        # 使用现有的LangGraph服务
        self.langgraph_service = LangGraphService(db_session, llm_service)
        
        # 状态适配器
        self.state_adapter = IntegratedStateAdapter()
        
        # 流式处理配置
        self.chunk_size = 1024  # 流式块大小
        self.max_response_time = 30  # 最大响应时间（秒）
        self.heartbeat_interval = 5  # 心跳间隔（秒）
        
        # 活跃流式会话管理
        self.active_streams = {}  # session_id -> stream_info
        
        logger.info("流式处理管理器初始化完成")
    
    async def stream_response(self, state: UnifiedFitnessState) -> AsyncGenerator[Dict[str, Any], None]:
        """流式响应处理 - 基于现有LangGraph流式API"""
        session_id = state["session_id"]
        conversation_id = state["conversation_id"]
        
        try:
            logger.info(f"开始流式响应: {session_id}")
            
            # 记录流式会话
            stream_info = {
                "session_id": session_id,
                "conversation_id": conversation_id,
                "start_time": time.time(),
                "status": "active",
                "chunks_sent": 0
            }
            self.active_streams[session_id] = stream_info
            
            # 发送开始信号
            yield {
                "type": "stream_start",
                "session_id": session_id,
                "conversation_id": conversation_id,
                "timestamp": datetime.now().isoformat()
            }
            
            # 转换状态为LangGraph格式
            langgraph_state = await self.state_adapter.convert_from_unified(state, "langgraph")
            
            # 配置LangGraph流式处理
            config = {
                "configurable": {"session_id": session_id},
                "recursion_limit": 50,
                "stream_mode": "values"  # 流式输出值
            }
            
            # 使用现有LangGraph服务的流式API
            chunk_count = 0
            last_heartbeat = time.time()
            
            async for chunk in self.langgraph_service.graph.astream(langgraph_state, config=config):
                # 检查会话是否被中断
                if session_id not in self.active_streams or self.active_streams[session_id]["status"] != "active":
                    logger.info(f"流式会话被中断: {session_id}")
                    break
                
                # 处理流式块
                processed_chunk = await self._process_stream_chunk(chunk, session_id)
                if processed_chunk:
                    yield processed_chunk
                    chunk_count += 1
                    stream_info["chunks_sent"] = chunk_count
                
                # 发送心跳（如果需要）
                current_time = time.time()
                if current_time - last_heartbeat > self.heartbeat_interval:
                    yield {
                        "type": "heartbeat",
                        "session_id": session_id,
                        "timestamp": datetime.now().isoformat(),
                        "chunks_sent": chunk_count
                    }
                    last_heartbeat = current_time
                
                # 检查超时
                if current_time - stream_info["start_time"] > self.max_response_time:
                    logger.warning(f"流式响应超时: {session_id}")
                    yield {
                        "type": "timeout",
                        "session_id": session_id,
                        "message": "响应超时，请重试"
                    }
                    break
            
            # 发送结束信号
            stream_info["status"] = "completed"
            stream_info["end_time"] = time.time()
            stream_info["duration"] = stream_info["end_time"] - stream_info["start_time"]
            
            yield {
                "type": "stream_end",
                "session_id": session_id,
                "conversation_id": conversation_id,
                "timestamp": datetime.now().isoformat(),
                "chunks_sent": chunk_count,
                "duration": stream_info["duration"]
            }
            
            logger.info(f"流式响应完成: {session_id}, 块数: {chunk_count}, 耗时: {stream_info['duration']:.2f}s")
            
        except Exception as e:
            logger.error(f"流式响应失败: {session_id}, 错误: {str(e)}")
            
            # 发送错误信号
            yield {
                "type": "error",
                "session_id": session_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            
            # 更新流式会话状态
            if session_id in self.active_streams:
                self.active_streams[session_id]["status"] = "error"
                self.active_streams[session_id]["error"] = str(e)
        
        finally:
            # 清理流式会话
            if session_id in self.active_streams:
                del self.active_streams[session_id]
    
    async def handle_interruption(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """处理流式响应中断"""
        session_id = state["session_id"]
        
        try:
            logger.info(f"处理流式响应中断: {session_id}")
            
            # 标记会话为中断状态
            if session_id in self.active_streams:
                self.active_streams[session_id]["status"] = "interrupted"
                self.active_streams[session_id]["interrupt_time"] = time.time()
            
            # 更新状态
            state["flow_state"]["stream_interrupted"] = True
            state["flow_state"]["interrupt_time"] = time.time()
            state["response_content"] = "流式响应已中断，您可以继续提问。"
            state["response_type"] = "interruption"
            
            return state
            
        except Exception as e:
            logger.error(f"处理流式中断失败: {session_id}, 错误: {str(e)}")
            state["error_count"] = state.get("error_count", 0) + 1
            return state
    
    async def _process_stream_chunk(self, chunk: Any, session_id: str) -> Optional[Dict[str, Any]]:
        """处理流式响应块"""
        try:
            # 处理不同类型的流式块
            if isinstance(chunk, dict):
                # 状态更新块
                if "response_content" in chunk:
                    return {
                        "type": "content",
                        "session_id": session_id,
                        "content": chunk["response_content"],
                        "timestamp": datetime.now().isoformat()
                    }
                elif "current_node" in chunk:
                    return {
                        "type": "node_update",
                        "session_id": session_id,
                        "current_node": chunk["current_node"],
                        "timestamp": datetime.now().isoformat()
                    }
                elif "structured_data" in chunk:
                    return {
                        "type": "structured_data",
                        "session_id": session_id,
                        "data": chunk["structured_data"],
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    # 通用状态更新
                    return {
                        "type": "state_update",
                        "session_id": session_id,
                        "update": chunk,
                        "timestamp": datetime.now().isoformat()
                    }
            
            elif isinstance(chunk, str):
                # 文本内容块
                return {
                    "type": "text",
                    "session_id": session_id,
                    "content": chunk,
                    "timestamp": datetime.now().isoformat()
                }
            
            else:
                # 其他类型的块
                return {
                    "type": "raw",
                    "session_id": session_id,
                    "data": str(chunk),
                    "timestamp": datetime.now().isoformat()
                }
        
        except Exception as e:
            logger.error(f"处理流式块失败: {str(e)}")
            return {
                "type": "error",
                "session_id": session_id,
                "error": f"处理流式块失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    def get_active_streams(self) -> Dict[str, Dict[str, Any]]:
        """获取活跃的流式会话"""
        return self.active_streams.copy()
    
    def interrupt_stream(self, session_id: str) -> bool:
        """中断指定的流式会话"""
        if session_id in self.active_streams:
            self.active_streams[session_id]["status"] = "interrupted"
            logger.info(f"流式会话已中断: {session_id}")
            return True
        return False
    
    def get_stream_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取流式会话状态"""
        return self.active_streams.get(session_id)
    
    async def cleanup_expired_streams(self):
        """清理过期的流式会话"""
        current_time = time.time()
        expired_sessions = []
        
        for session_id, stream_info in self.active_streams.items():
            # 检查是否超时
            if current_time - stream_info["start_time"] > self.max_response_time * 2:
                expired_sessions.append(session_id)
        
        # 清理过期会话
        for session_id in expired_sessions:
            del self.active_streams[session_id]
            logger.info(f"清理过期流式会话: {session_id}")
        
        return len(expired_sessions)


class WebSocketStreamingHandler:
    """WebSocket流式处理器 - 基于现有WebSocket基础设施"""
    
    def __init__(self, streaming_processor: StreamingProcessor):
        self.streaming_processor = streaming_processor
        self.active_connections = {}  # session_id -> websocket
        
        logger.info("WebSocket流式处理器初始化完成")
    
    async def handle_websocket_connection(self, websocket, session_id: str):
        """处理WebSocket连接"""
        try:
            logger.info(f"WebSocket连接建立: {session_id}")
            self.active_connections[session_id] = websocket
            
            # 发送连接确认
            await websocket.send_json({
                "type": "connection_established",
                "session_id": session_id,
                "timestamp": datetime.now().isoformat()
            })
            
            # 保持连接活跃
            while True:
                try:
                    # 等待客户端消息
                    message = await websocket.receive_json()
                    await self._handle_websocket_message(websocket, session_id, message)
                
                except Exception as e:
                    logger.error(f"WebSocket消息处理失败: {session_id}, 错误: {str(e)}")
                    break
        
        except Exception as e:
            logger.error(f"WebSocket连接处理失败: {session_id}, 错误: {str(e)}")
        
        finally:
            # 清理连接
            if session_id in self.active_connections:
                del self.active_connections[session_id]
            logger.info(f"WebSocket连接关闭: {session_id}")
    
    async def _handle_websocket_message(self, websocket, session_id: str, message: Dict[str, Any]):
        """处理WebSocket消息"""
        message_type = message.get("type")
        
        if message_type == "start_stream":
            # 开始流式响应
            state = message.get("state")
            if state:
                await self._start_streaming_response(websocket, session_id, state)
        
        elif message_type == "interrupt_stream":
            # 中断流式响应
            self.streaming_processor.interrupt_stream(session_id)
            await websocket.send_json({
                "type": "stream_interrupted",
                "session_id": session_id,
                "timestamp": datetime.now().isoformat()
            })
        
        elif message_type == "ping":
            # 心跳检测
            await websocket.send_json({
                "type": "pong",
                "session_id": session_id,
                "timestamp": datetime.now().isoformat()
            })
        
        else:
            logger.warning(f"未知的WebSocket消息类型: {message_type}")
    
    async def _start_streaming_response(self, websocket, session_id: str, state_data: Dict[str, Any]):
        """开始流式响应"""
        try:
            # 转换状态数据为统一状态
            unified_state = await self.streaming_processor.state_adapter.convert_to_unified(
                state_data, "dict", session_id
            )
            
            # 开始流式处理
            async for chunk in self.streaming_processor.stream_response(unified_state):
                await websocket.send_json(chunk)
        
        except Exception as e:
            logger.error(f"流式响应失败: {session_id}, 错误: {str(e)}")
            await websocket.send_json({
                "type": "error",
                "session_id": session_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
    
    def get_active_connections(self) -> Dict[str, Any]:
        """获取活跃的WebSocket连接"""
        return {session_id: "connected" for session_id in self.active_connections.keys()}
    
    async def broadcast_message(self, message: Dict[str, Any], target_sessions: Optional[List[str]] = None):
        """广播消息到指定会话"""
        target_connections = self.active_connections
        
        if target_sessions:
            target_connections = {
                session_id: websocket 
                for session_id, websocket in self.active_connections.items()
                if session_id in target_sessions
            }
        
        for session_id, websocket in target_connections.items():
            try:
                await websocket.send_json(message)
            except Exception as e:
                logger.error(f"广播消息失败: {session_id}, 错误: {str(e)}")
    
    async def close_connection(self, session_id: str):
        """关闭指定的WebSocket连接"""
        if session_id in self.active_connections:
            websocket = self.active_connections[session_id]
            try:
                await websocket.close()
            except Exception as e:
                logger.error(f"关闭WebSocket连接失败: {session_id}, 错误: {str(e)}")
            finally:
                del self.active_connections[session_id]
