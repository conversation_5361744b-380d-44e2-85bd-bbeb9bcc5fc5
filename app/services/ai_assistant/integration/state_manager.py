"""
整合状态管理器

基于现有组件，统一管理三套系统的状态：
- LangGraph检查点存储 (PostgreSQL/Memory)
- 原始系统缓存 (Redis/Memory)
- 数据库持久化存储

实现多源状态获取策略和智能缓存机制。
"""

import json
import time
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from redis import Redis
from datetime import datetime

from .interfaces import StateManagerInterface
from .state_adapter import IntegratedStateAdapter
from ..langgraph.state_definitions import UnifiedFitnessState

# 导入现有的状态管理组件
from ...memory_cache_service import MemoryCacheService

# 导入数据库相关
from ...logger.logger import get_logger

# 创建简单的对话状态管理器
class ConversationStateManager:
    def __init__(self):
        pass

    async def get_conversation_state(self, conversation_id: str):
        """获取对话状态"""
        return None

    async def save_conversation_state(self, conversation_id: str, state):
        """保存对话状态"""
        return True

    async def delete_conversation_state(self, conversation_id: str):
        """删除对话状态"""
        return True

# 创建简单的检查点存储器
class PostgreSQLCheckpointer:
    def __init__(self, db):
        self.db = db

    async def aget_checkpoint(self, conversation_id: str, thread_id: str):
        """获取检查点"""
        return None

    async def aput_checkpoint(self, conversation_id: str, thread_id: str, state):
        """保存检查点"""
        return True

    async def adelete_checkpoint(self, conversation_id: str, thread_id: str):
        """删除检查点"""
        return True

logger = get_logger(__name__)


class IntegratedStateManager(StateManagerInterface):
    """整合状态管理器 - 基于现有组件，统一管理三套系统的状态"""
    
    def __init__(self, db: Session, redis_client: Optional[Redis] = None):
        self.db = db
        self.redis_client = redis_client
        
        # 状态适配器
        self.state_adapter = IntegratedStateAdapter()
        
        # 使用现有的状态管理组件
        self.conversation_state_manager = ConversationStateManager()
        self.memory_cache_service = MemoryCacheService()
        
        # 检查点存储器 - 使用简单实现
        try:
            self.checkpointer = PostgreSQLCheckpointer(db)
            logger.info("使用PostgreSQL检查点存储")
        except Exception as e:
            logger.warning(f"PostgreSQL检查点存储初始化失败: {str(e)}")
            self.checkpointer = PostgreSQLCheckpointer(db)  # 使用简单实现
        
        # 状态缓存配置 - 基于现有缓存服务
        self.state_cache_ttl = 3600  # 1小时
        self.session_cache_ttl = 1800  # 30分钟
        
        logger.info("整合状态管理器初始化完成")
    
    async def get_current_state(self, conversation_id: str) -> UnifiedFitnessState:
        """获取当前统一状态 - 多源状态获取策略"""
        try:
            logger.info(f"获取会话状态: {conversation_id}")
            
            # 1. 尝试从缓存获取
            cached_state = await self._get_from_cache(conversation_id)
            if cached_state:
                logger.debug(f"从缓存获取状态成功: {conversation_id}")
                return cached_state
            
            # 2. 从LangGraph检查点恢复
            checkpoint_state = await self._get_from_langgraph_checkpoint(conversation_id)
            if checkpoint_state:
                logger.debug(f"从LangGraph检查点恢复状态: {conversation_id}")
                await self._cache_state(conversation_id, checkpoint_state)
                return checkpoint_state
            
            # 3. 从原始系统恢复
            original_state = await self._get_from_original_system(conversation_id)
            if original_state:
                logger.debug(f"从原始系统恢复状态: {conversation_id}")
                await self._cache_state(conversation_id, original_state)
                return original_state
            
            # 4. 创建新状态
            logger.info(f"创建新会话状态: {conversation_id}")
            new_state = await self._create_new_state(conversation_id)
            await self._cache_state(conversation_id, new_state)
            return new_state
            
        except Exception as e:
            logger.error(f"获取状态失败: {conversation_id}, 错误: {str(e)}")
            return await self._create_emergency_state(conversation_id)
    
    async def save_state(self, state: UnifiedFitnessState) -> bool:
        """保存统一状态 - 多目标状态保存策略"""
        try:
            conversation_id = state["conversation_id"]
            logger.info(f"保存会话状态: {conversation_id}")
            
            # 保存结果追踪
            save_results = {}
            
            # 1. 保存到LangGraph检查点
            try:
                await self._save_to_langgraph_checkpoint(state)
                save_results["langgraph"] = True
                logger.debug(f"LangGraph检查点保存成功: {conversation_id}")
            except Exception as e:
                save_results["langgraph"] = False
                logger.error(f"LangGraph检查点保存失败: {conversation_id}, {str(e)}")
            
            # 2. 转换并保存到原始系统格式
            try:
                await self._save_to_original_system(state)
                save_results["original"] = True
                logger.debug(f"原始系统状态保存成功: {conversation_id}")
            except Exception as e:
                save_results["original"] = False
                logger.error(f"原始系统状态保存失败: {conversation_id}, {str(e)}")
            
            # 3. 更新缓存
            try:
                await self._cache_state(conversation_id, state)
                save_results["cache"] = True
                logger.debug(f"缓存状态更新成功: {conversation_id}")
            except Exception as e:
                save_results["cache"] = False
                logger.error(f"缓存状态更新失败: {conversation_id}, {str(e)}")
            
            # 判断保存是否成功（至少一个存储成功）
            success_count = sum(save_results.values())
            if success_count > 0:
                logger.info(f"状态保存成功: {conversation_id}, 成功存储数: {success_count}/3")
                return True
            else:
                logger.error(f"状态保存完全失败: {conversation_id}")
                return False
                
        except Exception as e:
            logger.error(f"保存状态异常: {str(e)}")
            return False
    
    async def delete_state(self, conversation_id: str) -> bool:
        """删除会话状态"""
        try:
            logger.info(f"删除会话状态: {conversation_id}")
            
            # 删除结果追踪
            delete_results = {}
            
            # 1. 删除LangGraph检查点
            try:
                await self._delete_langgraph_checkpoint(conversation_id)
                delete_results["langgraph"] = True
            except Exception as e:
                delete_results["langgraph"] = False
                logger.error(f"删除LangGraph检查点失败: {str(e)}")
            
            # 2. 删除原始系统状态
            try:
                await self._delete_original_system_state(conversation_id)
                delete_results["original"] = True
            except Exception as e:
                delete_results["original"] = False
                logger.error(f"删除原始系统状态失败: {str(e)}")
            
            # 3. 删除缓存
            try:
                await self._delete_cache_state(conversation_id)
                delete_results["cache"] = True
            except Exception as e:
                delete_results["cache"] = False
                logger.error(f"删除缓存状态失败: {str(e)}")
            
            success_count = sum(delete_results.values())
            logger.info(f"状态删除完成: {conversation_id}, 成功删除数: {success_count}/3")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"删除状态异常: {str(e)}")
            return False
    
    async def migrate_from_legacy(self, conversation_id: str) -> Optional[UnifiedFitnessState]:
        """从原始系统迁移状态"""
        try:
            # 从数据库获取会话记录
            conversation = crud_conversation.get_by_session_id(self.db, session_id=conversation_id)
            if not conversation:
                return None
            
            # 获取消息历史
            messages = crud_message.get_by_conversation_id(self.db, conversation_id=conversation.id)
            
            # 构建原始系统状态
            legacy_state = {
                "conversation_id": conversation_id,
                "user_id": str(conversation.user_id) if conversation.user_id else "",
                "session_id": conversation_id,
                "messages": [{"role": "user" if msg.is_user else "assistant", 
                            "content": msg.content} for msg in messages],
                "user_profile": conversation.metadata.get("user_info", {}) if conversation.metadata else {},
                "training_params": conversation.metadata.get("training_params", {}) if conversation.metadata else {},
                "intent": conversation.metadata.get("intent", "") if conversation.metadata else "",
                "confidence": conversation.metadata.get("confidence", 0.0) if conversation.metadata else 0.0
            }
            
            # 转换为统一状态
            unified_state = await self.state_adapter.convert_to_unified(
                legacy_state, "dict", conversation_id
            )
            
            logger.info(f"成功迁移原始系统状态: {conversation_id}")
            return unified_state
            
        except Exception as e:
            logger.error(f"迁移原始系统状态失败: {conversation_id}, 错误: {str(e)}")
            return None
    
    async def _get_from_cache(self, conversation_id: str) -> Optional[UnifiedFitnessState]:
        """从缓存获取状态"""
        try:
            # 尝试从内存缓存获取
            cache_key = f"unified_state:{conversation_id}"
            cached_data = self.memory_cache_service.get_session_state(cache_key)
            if cached_data:
                return cached_data
            
            # 尝试从Redis缓存获取
            if self.redis_client:
                redis_data = self.redis_client.get(cache_key)
                if redis_data:
                    return json.loads(redis_data)
            
            return None
        except Exception as e:
            logger.error(f"缓存获取失败: {str(e)}")
            return None
    
    async def _cache_state(self, conversation_id: str, state: UnifiedFitnessState) -> bool:
        """缓存状态"""
        try:
            cache_key = f"unified_state:{conversation_id}"
            
            # 缓存到内存
            self.memory_cache_service.set_session_state(cache_key, state, ttl=self.session_cache_ttl)
            
            # 缓存到Redis
            if self.redis_client:
                self.redis_client.setex(
                    cache_key, 
                    self.state_cache_ttl,
                    json.dumps(state, default=str)
                )
            
            return True
        except Exception as e:
            logger.error(f"缓存状态失败: {str(e)}")
            return False
    
    async def _get_from_langgraph_checkpoint(self, conversation_id: str) -> Optional[UnifiedFitnessState]:
        """从LangGraph检查点获取状态"""
        try:
            # 使用检查点存储器获取状态
            checkpoint = await self.checkpointer.aget_checkpoint(conversation_id, "fitness_ai")
            if checkpoint and checkpoint.state:
                # 转换为统一状态
                return await self.state_adapter.convert_to_unified(
                    checkpoint.state, "langgraph", conversation_id
                )
            return None
        except Exception as e:
            logger.error(f"从LangGraph检查点获取状态失败: {str(e)}")
            return None
    
    async def _get_from_original_system(self, conversation_id: str) -> Optional[UnifiedFitnessState]:
        """从原始系统获取状态"""
        try:
            # 使用原始系统状态管理器
            original_state = await self.conversation_state_manager.get_conversation_state(conversation_id)
            if original_state:
                return await self.state_adapter.convert_to_unified(
                    original_state, "conversation_state", conversation_id
                )
            return None
        except Exception as e:
            logger.error(f"从原始系统获取状态失败: {str(e)}")
            return None
    
    async def _create_new_state(self, conversation_id: str) -> UnifiedFitnessState:
        """创建新状态"""
        return await self.state_adapter._create_default_state(conversation_id)
    
    async def _create_emergency_state(self, conversation_id: str) -> UnifiedFitnessState:
        """创建紧急状态"""
        emergency_state = await self.state_adapter._create_default_state(conversation_id)
        emergency_state["error_count"] = 1
        emergency_state["response_content"] = "系统遇到问题，正在恢复中..."
        emergency_state["response_type"] = "error"
        return emergency_state
    
    async def _save_to_langgraph_checkpoint(self, state: UnifiedFitnessState):
        """保存到LangGraph检查点"""
        await self.checkpointer.aput_checkpoint(
            state["conversation_id"], "fitness_ai", state
        )
    
    async def _save_to_original_system(self, state: UnifiedFitnessState):
        """保存到原始系统"""
        original_format = await self.state_adapter.convert_from_unified(state, "conversation_state")
        await self.conversation_state_manager.save_conversation_state(
            state["conversation_id"], original_format
        )
    
    async def _delete_langgraph_checkpoint(self, conversation_id: str):
        """删除LangGraph检查点"""
        await self.checkpointer.adelete_checkpoint(conversation_id, "fitness_ai")
    
    async def _delete_original_system_state(self, conversation_id: str):
        """删除原始系统状态"""
        await self.conversation_state_manager.delete_conversation_state(conversation_id)
    
    async def _delete_cache_state(self, conversation_id: str):
        """删除缓存状态"""
        cache_key = f"unified_state:{conversation_id}"
        
        # 删除内存缓存
        self.memory_cache_service.delete_session_state(cache_key)
        
        # 删除Redis缓存
        if self.redis_client:
            self.redis_client.delete(cache_key)
