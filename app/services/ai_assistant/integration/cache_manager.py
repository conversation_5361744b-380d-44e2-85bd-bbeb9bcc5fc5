"""
智能缓存管理器

多层缓存策略和智能缓存优化：
- L1内存缓存 + L2 Redis缓存 + L3数据库缓存
- 缓存预热和智能失效策略
- 缓存一致性保证
- 性能监控和自动优化

目标：缓存命中率>90%，响应时间<100ms
"""

import asyncio
import time
import json
import hashlib
from typing import Dict, Any, Optional, List, Union, Callable
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, asdict

from .interfaces import CacheManagerInterface
from ...logger.logger import get_logger

logger = get_logger(__name__)


class CacheLevel(Enum):
    """缓存层级"""
    L1_MEMORY = "l1_memory"       # L1内存缓存
    L2_REDIS = "l2_redis"         # L2 Redis缓存
    L3_DATABASE = "l3_database"   # L3数据库缓存


class CacheStrategy(Enum):
    """缓存策略"""
    LRU = "lru"                   # 最近最少使用
    LFU = "lfu"                   # 最少使用频率
    TTL = "ttl"                   # 基于时间
    ADAPTIVE = "adaptive"         # 自适应策略


@dataclass
class CacheItem:
    """缓存项"""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int
    ttl: Optional[int] = None
    size: int = 0
    metadata: Dict[str, Any] = None


@dataclass
class CacheStats:
    """缓存统计"""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    evictions: int = 0
    total_size: int = 0
    
    @property
    def hit_rate(self) -> float:
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0


class LRUCache:
    """LRU内存缓存实现"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: Dict[str, CacheItem] = {}
        self.access_order: List[str] = []
        self.stats = CacheStats()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        if key not in self.cache:
            self.stats.misses += 1
            return None
        
        item = self.cache[key]
        
        # 检查TTL
        if item.ttl and (datetime.now() - item.created_at).seconds > item.ttl:
            self.delete(key)
            self.stats.misses += 1
            return None
        
        # 更新访问信息
        item.last_accessed = datetime.now()
        item.access_count += 1
        
        # 更新访问顺序
        if key in self.access_order:
            self.access_order.remove(key)
        self.access_order.append(key)
        
        self.stats.hits += 1
        return item.value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存项"""
        try:
            # 计算大小
            size = len(str(value))
            
            # 创建缓存项
            item = CacheItem(
                key=key,
                value=value,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                access_count=1,
                ttl=ttl or self.default_ttl,
                size=size
            )
            
            # 检查是否需要淘汰
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            # 设置缓存
            self.cache[key] = item
            if key not in self.access_order:
                self.access_order.append(key)
            
            self.stats.sets += 1
            self.stats.total_size += size
            
            return True
            
        except Exception as e:
            logger.error(f"LRU缓存设置失败: {str(e)}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        if key in self.cache:
            item = self.cache[key]
            self.stats.total_size -= item.size
            del self.cache[key]
            
            if key in self.access_order:
                self.access_order.remove(key)
            
            self.stats.deletes += 1
            return True
        return False
    
    def _evict_lru(self):
        """淘汰最近最少使用的项"""
        if self.access_order:
            lru_key = self.access_order[0]
            self.delete(lru_key)
            self.stats.evictions += 1
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_order.clear()
        self.stats = CacheStats()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "hit_rate": self.stats.hit_rate,
            "hits": self.stats.hits,
            "misses": self.stats.misses,
            "sets": self.stats.sets,
            "deletes": self.stats.deletes,
            "evictions": self.stats.evictions,
            "total_size": self.stats.total_size
        }


class IntelligentCacheManager(CacheManagerInterface):
    """智能缓存管理器"""
    
    def __init__(self, redis_client=None, db_session=None):
        self.redis = redis_client
        self.db = db_session
        
        # L1内存缓存
        self.l1_cache = LRUCache(max_size=1000, default_ttl=300)  # 5分钟TTL
        
        # 缓存配置
        self.cache_configs = self._init_cache_configs()
        
        # 缓存统计
        self.global_stats = {
            "l1_hits": 0,
            "l2_hits": 0,
            "l3_hits": 0,
            "total_misses": 0,
            "preload_hits": 0,
            "invalidations": 0
        }
        
        # 预热缓存键
        self.preload_keys = set()
        
        # 缓存依赖关系
        self.cache_dependencies: Dict[str, List[str]] = {}
        
        logger.info("智能缓存管理器初始化完成")
    
    def _init_cache_configs(self) -> Dict[str, Dict[str, Any]]:
        """初始化缓存配置"""
        return {
            "user_profile": {
                "ttl": 1800,  # 30分钟
                "levels": [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS],
                "preload": True,
                "strategy": CacheStrategy.LRU
            },
            "training_params": {
                "ttl": 900,   # 15分钟
                "levels": [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS],
                "preload": False,
                "strategy": CacheStrategy.TTL
            },
            "exercise_data": {
                "ttl": 3600,  # 1小时
                "levels": [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS, CacheLevel.L3_DATABASE],
                "preload": True,
                "strategy": CacheStrategy.LFU
            },
            "llm_responses": {
                "ttl": 600,   # 10分钟
                "levels": [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS],
                "preload": False,
                "strategy": CacheStrategy.ADAPTIVE
            },
            "workflow_state": {
                "ttl": 300,   # 5分钟
                "levels": [CacheLevel.L1_MEMORY],
                "preload": False,
                "strategy": CacheStrategy.TTL
            }
        }
    
    async def get(self, key: str, cache_type: str = "default") -> Optional[Any]:
        """多层缓存获取"""
        start_time = time.time()
        
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(key, cache_type)
            config = self.cache_configs.get(cache_type, self.cache_configs["training_params"])
            
            # L1内存缓存
            if CacheLevel.L1_MEMORY in config["levels"]:
                value = self.l1_cache.get(cache_key)
                if value is not None:
                    self.global_stats["l1_hits"] += 1
                    logger.debug(f"L1缓存命中: {cache_key}")
                    return value
            
            # L2 Redis缓存
            if CacheLevel.L2_REDIS in config["levels"] and self.redis:
                value = await self._get_from_redis(cache_key)
                if value is not None:
                    # 回填L1缓存
                    if CacheLevel.L1_MEMORY in config["levels"]:
                        self.l1_cache.set(cache_key, value, config["ttl"])
                    
                    self.global_stats["l2_hits"] += 1
                    logger.debug(f"L2缓存命中: {cache_key}")
                    return value
            
            # L3数据库缓存
            if CacheLevel.L3_DATABASE in config["levels"] and self.db:
                value = await self._get_from_database(cache_key, cache_type)
                if value is not None:
                    # 回填上层缓存
                    await self._backfill_cache(cache_key, value, config)
                    
                    self.global_stats["l3_hits"] += 1
                    logger.debug(f"L3缓存命中: {cache_key}")
                    return value
            
            # 缓存未命中
            self.global_stats["total_misses"] += 1
            logger.debug(f"缓存未命中: {cache_key}")
            return None
            
        except Exception as e:
            logger.error(f"缓存获取失败: {str(e)}")
            return None
        
        finally:
            processing_time = time.time() - start_time
            if processing_time > 0.1:  # 超过100ms记录警告
                logger.warning(f"缓存获取耗时过长: {cache_key}, 耗时: {processing_time:.3f}s")
    
    async def set(self, key: str, value: Any, cache_type: str = "default", ttl: Optional[int] = None) -> bool:
        """多层缓存设置"""
        try:
            cache_key = self._generate_cache_key(key, cache_type)
            config = self.cache_configs.get(cache_type, self.cache_configs["training_params"])
            effective_ttl = ttl or config["ttl"]
            
            success = True
            
            # L1内存缓存
            if CacheLevel.L1_MEMORY in config["levels"]:
                if not self.l1_cache.set(cache_key, value, effective_ttl):
                    success = False
            
            # L2 Redis缓存
            if CacheLevel.L2_REDIS in config["levels"] and self.redis:
                if not await self._set_to_redis(cache_key, value, effective_ttl):
                    success = False
            
            # L3数据库缓存
            if CacheLevel.L3_DATABASE in config["levels"] and self.db:
                if not await self._set_to_database(cache_key, value, cache_type, effective_ttl):
                    success = False
            
            # 处理缓存依赖
            await self._handle_cache_dependencies(cache_key, cache_type)
            
            if success:
                logger.debug(f"缓存设置成功: {cache_key}")
            
            return success
            
        except Exception as e:
            logger.error(f"缓存设置失败: {str(e)}")
            return False
    
    async def delete(self, key: str, cache_type: str = "default") -> bool:
        """多层缓存删除"""
        try:
            cache_key = self._generate_cache_key(key, cache_type)
            config = self.cache_configs.get(cache_type, self.cache_configs["training_params"])
            
            success = True
            
            # L1内存缓存
            if CacheLevel.L1_MEMORY in config["levels"]:
                self.l1_cache.delete(cache_key)
            
            # L2 Redis缓存
            if CacheLevel.L2_REDIS in config["levels"] and self.redis:
                try:
                    await self.redis.delete(cache_key)
                except Exception as e:
                    logger.warning(f"Redis删除失败: {str(e)}")
                    success = False
            
            # L3数据库缓存
            if CacheLevel.L3_DATABASE in config["levels"] and self.db:
                if not await self._delete_from_database(cache_key):
                    success = False
            
            # 处理依赖缓存失效
            await self._invalidate_dependent_caches(cache_key)
            
            self.global_stats["invalidations"] += 1
            logger.debug(f"缓存删除: {cache_key}")
            
            return success
            
        except Exception as e:
            logger.error(f"缓存删除失败: {str(e)}")
            return False
    
    async def preload_cache(self, cache_type: str, keys: List[str]) -> int:
        """缓存预热"""
        try:
            config = self.cache_configs.get(cache_type)
            if not config or not config.get("preload", False):
                return 0
            
            preloaded_count = 0
            
            for key in keys:
                try:
                    # 检查是否已缓存
                    cached_value = await self.get(key, cache_type)
                    if cached_value is not None:
                        continue
                    
                    # 从数据源加载
                    value = await self._load_from_source(key, cache_type)
                    if value is not None:
                        await self.set(key, value, cache_type)
                        self.preload_keys.add(self._generate_cache_key(key, cache_type))
                        preloaded_count += 1
                        self.global_stats["preload_hits"] += 1
                
                except Exception as e:
                    logger.warning(f"预热缓存失败: {key}, 错误: {str(e)}")
            
            logger.info(f"缓存预热完成: {cache_type}, 预热数量: {preloaded_count}")
            return preloaded_count
            
        except Exception as e:
            logger.error(f"缓存预热失败: {str(e)}")
            return 0
    
    async def invalidate_pattern(self, pattern: str, cache_type: str = "default") -> int:
        """按模式失效缓存"""
        try:
            invalidated_count = 0
            
            # L1内存缓存模式匹配
            keys_to_delete = []
            for key in self.l1_cache.cache.keys():
                if pattern in key:
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                self.l1_cache.delete(key)
                invalidated_count += 1
            
            # L2 Redis缓存模式匹配
            if self.redis:
                try:
                    redis_keys = await self.redis.keys(f"*{pattern}*")
                    if redis_keys:
                        await self.redis.delete(*redis_keys)
                        invalidated_count += len(redis_keys)
                except Exception as e:
                    logger.warning(f"Redis模式失效失败: {str(e)}")
            
            logger.info(f"模式失效完成: {pattern}, 失效数量: {invalidated_count}")
            return invalidated_count
            
        except Exception as e:
            logger.error(f"模式失效失败: {str(e)}")
            return 0

    def _generate_cache_key(self, key: str, cache_type: str) -> str:
        """生成缓存键"""
        # 使用MD5哈希确保键的唯一性和长度控制
        key_data = f"{cache_type}:{key}"
        return f"cache:{hashlib.md5(key_data.encode()).hexdigest()}"

    async def _get_from_redis(self, cache_key: str) -> Optional[Any]:
        """从Redis获取缓存"""
        try:
            data = await self.redis.get(cache_key)
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.warning(f"Redis获取失败: {str(e)}")
            return None

    async def _set_to_redis(self, cache_key: str, value: Any, ttl: int) -> bool:
        """设置Redis缓存"""
        try:
            data = json.dumps(value, default=str)
            await self.redis.setex(cache_key, ttl, data)
            return True
        except Exception as e:
            logger.warning(f"Redis设置失败: {str(e)}")
            return False

    async def _get_from_database(self, cache_key: str, cache_type: str) -> Optional[Any]:
        """从数据库获取缓存"""
        try:
            # 这里应该根据实际的数据库表结构实现
            # 简化实现，实际应该查询专门的缓存表
            return None
        except Exception as e:
            logger.warning(f"数据库缓存获取失败: {str(e)}")
            return None

    async def _set_to_database(self, cache_key: str, value: Any, cache_type: str, ttl: int) -> bool:
        """设置数据库缓存"""
        try:
            # 这里应该根据实际的数据库表结构实现
            # 简化实现，实际应该插入到专门的缓存表
            return True
        except Exception as e:
            logger.warning(f"数据库缓存设置失败: {str(e)}")
            return False

    async def _delete_from_database(self, cache_key: str) -> bool:
        """从数据库删除缓存"""
        try:
            # 这里应该根据实际的数据库表结构实现
            return True
        except Exception as e:
            logger.warning(f"数据库缓存删除失败: {str(e)}")
            return False

    async def _backfill_cache(self, cache_key: str, value: Any, config: Dict[str, Any]):
        """回填上层缓存"""
        try:
            # 回填L2 Redis缓存
            if CacheLevel.L2_REDIS in config["levels"] and self.redis:
                await self._set_to_redis(cache_key, value, config["ttl"])

            # 回填L1内存缓存
            if CacheLevel.L1_MEMORY in config["levels"]:
                self.l1_cache.set(cache_key, value, config["ttl"])

        except Exception as e:
            logger.warning(f"缓存回填失败: {str(e)}")

    async def _load_from_source(self, key: str, cache_type: str) -> Optional[Any]:
        """从数据源加载数据"""
        try:
            # 这里应该根据cache_type从相应的数据源加载数据
            # 简化实现，实际应该调用相应的服务或数据访问层

            if cache_type == "user_profile":
                # 从用户服务加载用户信息
                return await self._load_user_profile(key)
            elif cache_type == "exercise_data":
                # 从运动数据服务加载运动信息
                return await self._load_exercise_data(key)
            elif cache_type == "training_params":
                # 从训练参数服务加载参数
                return await self._load_training_params(key)

            return None

        except Exception as e:
            logger.warning(f"数据源加载失败: {key}, 错误: {str(e)}")
            return None

    async def _load_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """加载用户档案"""
        # 简化实现，实际应该查询用户表
        return {"user_id": user_id, "loaded_from": "database", "timestamp": datetime.now().isoformat()}

    async def _load_exercise_data(self, exercise_id: str) -> Optional[Dict[str, Any]]:
        """加载运动数据"""
        # 简化实现，实际应该查询运动表
        return {"exercise_id": exercise_id, "loaded_from": "database", "timestamp": datetime.now().isoformat()}

    async def _load_training_params(self, param_key: str) -> Optional[Dict[str, Any]]:
        """加载训练参数"""
        # 简化实现，实际应该查询参数表
        return {"param_key": param_key, "loaded_from": "database", "timestamp": datetime.now().isoformat()}

    async def _handle_cache_dependencies(self, cache_key: str, cache_type: str):
        """处理缓存依赖关系"""
        try:
            # 定义缓存依赖关系
            dependencies = {
                "user_profile": ["training_params", "workout_state"],
                "exercise_data": ["training_params", "workout_plans"],
                "training_params": ["workout_state"]
            }

            dependent_types = dependencies.get(cache_type, [])

            for dep_type in dependent_types:
                # 失效相关缓存
                await self.invalidate_pattern(dep_type, dep_type)

        except Exception as e:
            logger.warning(f"缓存依赖处理失败: {str(e)}")

    async def _invalidate_dependent_caches(self, cache_key: str):
        """失效依赖缓存"""
        try:
            if cache_key in self.cache_dependencies:
                dependent_keys = self.cache_dependencies[cache_key]

                for dep_key in dependent_keys:
                    await self.delete(dep_key)

                # 清理依赖关系
                del self.cache_dependencies[cache_key]

        except Exception as e:
            logger.warning(f"依赖缓存失效失败: {str(e)}")

    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            l1_stats = self.l1_cache.get_stats()

            total_hits = (self.global_stats["l1_hits"] +
                         self.global_stats["l2_hits"] +
                         self.global_stats["l3_hits"])
            total_requests = total_hits + self.global_stats["total_misses"]
            overall_hit_rate = total_hits / total_requests if total_requests > 0 else 0

            return {
                "overall": {
                    "hit_rate": overall_hit_rate,
                    "total_requests": total_requests,
                    "total_hits": total_hits,
                    "total_misses": self.global_stats["total_misses"],
                    "preload_hits": self.global_stats["preload_hits"],
                    "invalidations": self.global_stats["invalidations"]
                },
                "l1_memory": l1_stats,
                "l2_redis": {
                    "hits": self.global_stats["l2_hits"],
                    "available": self.redis is not None
                },
                "l3_database": {
                    "hits": self.global_stats["l3_hits"],
                    "available": self.db is not None
                },
                "preload_keys_count": len(self.preload_keys),
                "cache_dependencies_count": len(self.cache_dependencies)
            }

        except Exception as e:
            logger.error(f"获取缓存统计失败: {str(e)}")
            return {}

    async def clear_all_cache(self):
        """清空所有缓存"""
        try:
            # 清空L1内存缓存
            self.l1_cache.clear()

            # 清空L2 Redis缓存
            if self.redis:
                await self.redis.flushdb()

            # 重置统计
            self.global_stats = {
                "l1_hits": 0,
                "l2_hits": 0,
                "l3_hits": 0,
                "total_misses": 0,
                "preload_hits": 0,
                "invalidations": 0
            }

            self.preload_keys.clear()
            self.cache_dependencies.clear()

            logger.info("所有缓存已清空")

        except Exception as e:
            logger.error(f"清空缓存失败: {str(e)}")
