"""
训练模板服务类
提供训练模板相关的业务逻辑处理，包括数据查询、验证、转换等功能
"""

from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
import logging

from app.models.training_template import WorkoutTemplate
from app.models.workout_exercise import WorkoutExercise
from app.models.exercise import Exercise, ExerciseDetail
from app.models.set_record import SetRecord
from app.models.user import User
from app.schemas.training_plan import TrainingTemplateCreate
from app.services.exercise_data_processor import ExerciseDataProcessor
from app.services.set_record_manager import SetRecordManager

logger = logging.getLogger(__name__)


class TrainingTemplateService:
    """训练模板服务类"""

    def __init__(self, db: Session):
        self.db = db
        self.exercise_processor = ExerciseDataProcessor(db)
        self.set_record_manager = SetRecordManager(db)

    def get_template_with_details(self, template_id: int, user_id: int) -> Optional[WorkoutTemplate]:
        """
        获取包含完整详细信息的训练模板

        Args:
            template_id: 模板ID
            user_id: 用户ID

        Returns:
            训练模板对象，包含完整的关联数据
        """
        try:
            template = self.db.query(WorkoutTemplate).options(
                joinedload(WorkoutTemplate.template_exercises)
                .joinedload(WorkoutExercise.exercise)
                .joinedload(Exercise.details),
                joinedload(WorkoutTemplate.template_exercises)
                .joinedload(WorkoutExercise.set_records)
            ).filter(
                WorkoutTemplate.id == template_id,
                WorkoutTemplate.user_id == user_id
            ).first()

            return template
        except SQLAlchemyError as e:
            logger.error(f"获取训练模板失败: {str(e)}")
            return None

    def get_user_templates_with_details(self, user_id: int) -> List[WorkoutTemplate]:
        """
        获取用户的所有训练模板，包含完整详细信息

        Args:
            user_id: 用户ID

        Returns:
            训练模板列表
        """
        try:
            templates = self.db.query(WorkoutTemplate).options(
                joinedload(WorkoutTemplate.template_exercises)
                .joinedload(WorkoutExercise.exercise)
                .joinedload(Exercise.details),
                joinedload(WorkoutTemplate.template_exercises)
                .joinedload(WorkoutExercise.set_records)
            ).filter(
                WorkoutTemplate.user_id == user_id
            ).order_by(WorkoutTemplate.created_at.desc()).all()

            return templates
        except SQLAlchemyError as e:
            logger.error(f"获取用户训练模板失败: {str(e)}")
            return []

    def create_template(self, template_data: TrainingTemplateCreate, user_id: int) -> Optional[WorkoutTemplate]:
        """
        创建新的训练模板

        Args:
            template_data: 模板数据
            user_id: 用户ID

        Returns:
            创建的模板对象
        """
        try:
            # 创建新的训练模板
            new_template = WorkoutTemplate(
                user_id=user_id,
                name=template_data.name,
                description=template_data.description,
                estimated_duration=template_data.estimated_duration,
                target_body_parts=template_data.target_body_parts,
                training_scenario=template_data.training_scenario,
                visibility=template_data.visibility,
                notes=template_data.notes
            )

            self.db.add(new_template)
            self.db.flush()  # 获取模板ID

            # 创建关联的训练动作
            for idx, exercise_data in enumerate(template_data.exercises):
                # 处理exercise_id
                exercise_id = self.exercise_processor.resolve_exercise_id(exercise_data.exercise_id)

                workout_exercise = WorkoutExercise(
                    template_id=new_template.id,
                    exercise_id=exercise_id,
                    sets=exercise_data.sets,
                    reps=exercise_data.reps,
                    weight=exercise_data.weight,
                    rest_seconds=exercise_data.rest_seconds,
                    order=idx + 1,
                    notes=exercise_data.notes,
                    exercise_type=exercise_data.exercise_type,
                    superset_group=exercise_data.superset_group
                )
                self.db.add(workout_exercise)

            self.db.commit()

            # 重新查询以获取完整的关联数据
            return self.get_template_with_details(new_template.id, user_id)

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"创建训练模板失败: {str(e)}")
            return None

    def update_template(self, template_id: int, template_data: TrainingTemplateCreate, user_id: int) -> Optional[Dict[str, Any]]:
        """
        更新训练模板

        Args:
            template_id: 模板ID
            template_data: 更新数据
            user_id: 用户ID

        Returns:
            更新结果字典
        """
        try:
            # 获取现有模板
            existing_template = self.get_template_with_details(template_id, user_id)
            if not existing_template:
                return None

            # 更新基本字段
            template_fields_updated = self._update_template_fields(existing_template, template_data)

            # 更新训练动作
            exercises_updated = self._update_template_exercises(existing_template, template_data.exercises)

            # 提交更改
            if template_fields_updated or exercises_updated:
                self.db.commit()
                self.db.refresh(existing_template)

                return {
                    "message": "训练模板更新成功",
                    "updated_fields": template_fields_updated,
                    "exercises_updated": exercises_updated,
                    "template": existing_template.to_dict()
                }
            else:
                return {
                    "message": "训练模板无变更",
                    "updated_fields": [],
                    "exercises_updated": False,
                    "template": existing_template.to_dict()
                }

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"更新训练模板失败: {str(e)}")
            return None

    def _update_template_fields(self, template: WorkoutTemplate, template_data: TrainingTemplateCreate) -> List[str]:
        """更新模板基本字段"""
        updated_fields = []

        field_mappings = {
            'name': template_data.name,
            'description': template_data.description,
            'estimated_duration': template_data.estimated_duration,
            'target_body_parts': template_data.target_body_parts,
            'training_scenario': template_data.training_scenario,
            'visibility': template_data.visibility,
            'notes': template_data.notes
        }

        for field, new_value in field_mappings.items():
            current_value = getattr(template, field)
            if new_value != current_value:
                setattr(template, field, new_value)
                updated_fields.append(field)

        return updated_fields

    def _update_template_exercises(self, template: WorkoutTemplate, exercises_data: List) -> bool:
        """更新模板训练动作"""
        exercises_updated = False

        # 获取现有的训练动作
        existing_exercises = {ex.order: ex for ex in template.template_exercises}
        new_exercises_data = {idx + 1: ex for idx, ex in enumerate(exercises_data)}

        # 更新或创建训练动作
        for order, exercise_data in new_exercises_data.items():
            exercise_id = self.exercise_processor.resolve_exercise_id(exercise_data.exercise_id)

            if order in existing_exercises:
                # 更新现有动作
                if self._update_existing_exercise(existing_exercises[order], exercise_data, exercise_id):
                    exercises_updated = True
                # 更新组记录
                if self._update_exercise_set_records(existing_exercises[order], exercise_data):
                    exercises_updated = True
            else:
                # 创建新动作
                new_exercise = self._create_new_exercise(template.id, exercise_data, exercise_id, order)
                # 为新动作创建组记录
                if new_exercise and exercise_data.set_records:
                    self._create_exercise_set_records(new_exercise, exercise_data)
                exercises_updated = True

        # 删除不再需要的动作
        for order, existing_exercise in existing_exercises.items():
            if order not in new_exercises_data:
                self.db.delete(existing_exercise)
                exercises_updated = True

        return exercises_updated

    def _update_existing_exercise(self, existing_exercise: WorkoutExercise, exercise_data, exercise_id: int) -> bool:
        """更新现有训练动作"""
        updated = False

        field_mappings = {
            'exercise_id': exercise_id,
            'sets': exercise_data.sets,
            'reps': exercise_data.reps,
            'weight': exercise_data.weight,
            'rest_seconds': exercise_data.rest_seconds,
            'notes': exercise_data.notes,
            'exercise_type': exercise_data.exercise_type,
            'superset_group': exercise_data.superset_group
        }

        for field, new_value in field_mappings.items():
            current_value = getattr(existing_exercise, field)
            if new_value != current_value:
                setattr(existing_exercise, field, new_value)
                updated = True

        return updated

    def _create_new_exercise(self, template_id: int, exercise_data, exercise_id: int, order: int) -> WorkoutExercise:
        """创建新的训练动作"""
        new_workout_exercise = WorkoutExercise(
            template_id=template_id,
            exercise_id=exercise_id,
            sets=exercise_data.sets,
            reps=exercise_data.reps,
            weight=exercise_data.weight,
            rest_seconds=exercise_data.rest_seconds,
            order=order,
            notes=exercise_data.notes,
            exercise_type=exercise_data.exercise_type,
            superset_group=exercise_data.superset_group
        )
        self.db.add(new_workout_exercise)
        self.db.flush()  # 获取ID
        return new_workout_exercise

    def _update_exercise_set_records(self, workout_exercise: WorkoutExercise, exercise_data) -> bool:
        """更新训练动作的组记录"""
        if not exercise_data.set_records:
            return False

        updated = False

        # 获取现有的组记录
        existing_set_records = {sr.set_number: sr for sr in workout_exercise.set_records}

        # 处理新的组记录数据
        for set_record_data in exercise_data.set_records:
            set_number = set_record_data.set_number

            if set_number in existing_set_records:
                # 更新现有组记录
                existing_record = existing_set_records[set_number]
                if self._update_single_set_record(existing_record, set_record_data):
                    updated = True
            else:
                # 创建新的组记录
                self._create_single_set_record(workout_exercise.id, set_record_data)
                updated = True

        # 删除不再需要的组记录
        new_set_numbers = {sr.set_number for sr in exercise_data.set_records}
        for set_number, existing_record in existing_set_records.items():
            if set_number not in new_set_numbers:
                self.db.delete(existing_record)
                updated = True

        return updated

    def _create_exercise_set_records(self, workout_exercise: WorkoutExercise, exercise_data):
        """为新创建的训练动作创建组记录"""
        if not exercise_data.set_records:
            return

        for set_record_data in exercise_data.set_records:
            self._create_single_set_record(workout_exercise.id, set_record_data)

    def _update_single_set_record(self, existing_record, set_record_data) -> bool:
        """更新单个组记录"""
        updated = False

        field_mappings = {
            'set_type': set_record_data.set_type,
            'weight': set_record_data.weight,
            'reps': set_record_data.reps,
            'completed': set_record_data.completed,
            'notes': set_record_data.notes
        }

        for field, new_value in field_mappings.items():
            current_value = getattr(existing_record, field)
            if new_value != current_value:
                setattr(existing_record, field, new_value)
                updated = True

        return updated

    def _create_single_set_record(self, workout_exercise_id: int, set_record_data):
        """创建单个组记录"""
        # 使用SetRecordManager创建组记录
        set_data = {
            "set_number": set_record_data.set_number,
            "set_type": set_record_data.set_type,
            "weight": set_record_data.weight,
            "reps": set_record_data.reps,
            "completed": set_record_data.completed,
            "notes": set_record_data.notes
        }

        self.set_record_manager.create_set_record(workout_exercise_id, set_data)
