# AI健身助手系统故障处理手册

## 1. 故障分类与优先级

### 1.1 故障级别定义
| 级别 | 描述 | 响应时间 | 解决时间 |
|------|------|---------|---------|
| P0 - 严重 | 系统完全不可用 | 5分钟 | 1小时 |
| P1 - 高 | 核心功能受影响 | 15分钟 | 4小时 |
| P2 - 中 | 部分功能异常 | 1小时 | 24小时 |
| P3 - 低 | 性能问题或小功能异常 | 4小时 | 72小时 |

### 1.2 故障类型
- **服务故障**: 应用服务无响应或错误
- **数据库故障**: 数据库连接失败或性能问题
- **网络故障**: 网络连接异常或延迟
- **性能故障**: 响应时间过长或吞吐量下降
- **安全故障**: 安全漏洞或攻击

## 2. 快速诊断流程

### 2.1 故障诊断检查清单
```bash
# 1. 系统整体状态检查
curl -f http://localhost:8000/health
docker-compose ps
systemctl status docker

# 2. 资源使用检查
df -h                    # 磁盘空间
free -h                  # 内存使用
top                      # CPU使用
iostat -x 1 5           # IO状态

# 3. 网络连接检查
netstat -tulpn | grep :8000
ping -c 3 localhost
nslookup localhost

# 4. 日志快速检查
tail -100 /app/logs/app.log | grep ERROR
docker-compose logs --tail=100 ai-assistant | grep ERROR
journalctl -u docker --since "10 minutes ago"
```

### 2.2 故障定位决策树
```
故障报告
    ↓
服务是否响应？
    ├─ 否 → 检查服务状态 → 重启服务
    └─ 是 → 响应是否正常？
              ├─ 否 → 检查错误日志 → 分析错误类型
              └─ 是 → 性能是否正常？
                        ├─ 否 → 性能分析 → 优化处理
                        └─ 是 → 监控告警误报
```

## 3. 常见故障处理

### 3.1 服务无响应故障

#### 3.1.1 症状识别
- HTTP请求超时或连接拒绝
- 健康检查失败
- 负载均衡器报告后端不可用

#### 3.1.2 诊断步骤
```bash
# 1. 检查服务进程
ps aux | grep uvicorn
docker-compose ps ai-assistant

# 2. 检查端口监听
netstat -tulpn | grep :8000
ss -tulpn | grep :8000

# 3. 检查容器状态
docker inspect ai-assistant_ai-assistant_1

# 4. 检查资源限制
docker stats ai-assistant_ai-assistant_1
```

#### 3.1.3 解决方案
```bash
# 方案1: 重启服务
docker-compose restart ai-assistant

# 方案2: 如果重启失败，重新创建容器
docker-compose stop ai-assistant
docker-compose rm -f ai-assistant
docker-compose up -d ai-assistant

# 方案3: 如果资源不足，临时扩容
docker-compose up --scale ai-assistant=5

# 方案4: 检查并清理资源
docker system prune -f
```

### 3.2 数据库连接故障

#### 3.2.1 症状识别
- 应用日志显示数据库连接错误
- 数据库操作超时
- 连接池耗尽

#### 3.2.2 诊断步骤
```bash
# 1. 检查数据库服务
docker-compose ps postgres
pg_isready -h localhost -p 5432 -U postgres

# 2. 检查连接数
psql -h localhost -U postgres -c "
SELECT count(*) as total_connections,
       count(*) FILTER (WHERE state = 'active') as active_connections,
       count(*) FILTER (WHERE state = 'idle') as idle_connections
FROM pg_stat_activity;"

# 3. 检查长时间运行的查询
psql -h localhost -U postgres -c "
SELECT pid, usename, application_name, state, 
       now() - query_start as duration, query
FROM pg_stat_activity 
WHERE state != 'idle' 
ORDER BY duration DESC;"

# 4. 检查数据库日志
docker-compose logs postgres | tail -100
```

#### 3.2.3 解决方案
```bash
# 方案1: 终止长时间运行的查询
psql -h localhost -U postgres -c "
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE state = 'active' 
AND now() - query_start > interval '5 minutes';"

# 方案2: 重启数据库服务
docker-compose restart postgres

# 方案3: 增加连接池大小（临时）
# 修改应用配置中的数据库连接池设置

# 方案4: 数据库性能优化
psql -h localhost -U postgres -c "VACUUM ANALYZE;"
```

### 3.3 Redis缓存故障

#### 3.3.1 症状识别
- Redis连接失败
- 缓存命中率下降
- 内存使用率过高

#### 3.3.2 诊断步骤
```bash
# 1. 检查Redis服务
docker-compose ps redis
redis-cli ping

# 2. 检查内存使用
redis-cli info memory

# 3. 检查连接数
redis-cli info clients

# 4. 检查慢查询
redis-cli slowlog get 10

# 5. 检查键空间
redis-cli info keyspace
```

#### 3.3.3 解决方案
```bash
# 方案1: 清理过期键
redis-cli --scan --pattern "*" | head -1000 | xargs redis-cli del

# 方案2: 重启Redis服务
docker-compose restart redis

# 方案3: 内存优化
redis-cli config set maxmemory-policy allkeys-lru

# 方案4: 清理慢查询日志
redis-cli slowlog reset
```

### 3.4 性能问题故障

#### 3.4.1 症状识别
- 响应时间超过阈值
- 吞吐量下降
- CPU或内存使用率过高

#### 3.4.2 诊断步骤
```bash
# 1. 性能指标检查
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8000/health

# 2. 系统资源检查
top -p $(pgrep -f uvicorn)
iotop -o
htop

# 3. 应用性能分析
# 查看应用性能指标
curl http://localhost:8000/metrics | grep response_time

# 4. 数据库性能检查
psql -h localhost -U postgres -c "
SELECT query, calls, total_time, mean_time
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;"
```

#### 3.4.3 解决方案
```bash
# 方案1: 水平扩容
docker-compose up --scale ai-assistant=5

# 方案2: 优化数据库查询
psql -h localhost -U postgres -c "
SELECT pg_stat_statements_reset();"

# 方案3: 清理系统缓存
sync && echo 3 > /proc/sys/vm/drop_caches

# 方案4: 重启服务释放资源
docker-compose restart ai-assistant
```

### 3.5 网络连接故障

#### 3.5.1 症状识别
- 网络超时错误
- 连接重置
- DNS解析失败

#### 3.5.2 诊断步骤
```bash
# 1. 网络连通性测试
ping -c 5 localhost
telnet localhost 8000

# 2. DNS解析测试
nslookup localhost
dig localhost

# 3. 路由检查
traceroute localhost
ip route show

# 4. 防火墙检查
iptables -L
ufw status
```

#### 3.5.3 解决方案
```bash
# 方案1: 重启网络服务
systemctl restart networking

# 方案2: 清理DNS缓存
systemctl restart systemd-resolved

# 方案3: 检查防火墙规则
ufw allow 8000/tcp

# 方案4: 重启Docker网络
docker network prune -f
docker-compose down && docker-compose up -d
```

## 4. 紧急故障处理

### 4.1 系统完全不可用 (P0)

#### 4.1.1 立即响应 (5分钟内)
```bash
# 1. 确认故障范围
curl -f http://localhost:8000/health
curl -f http://localhost/health

# 2. 检查基础服务
docker-compose ps
systemctl status docker

# 3. 立即通知相关人员
# 发送紧急通知给技术负责人和运维团队
```

#### 4.1.2 紧急恢复 (1小时内)
```bash
# 1. 尝试快速重启
docker-compose restart

# 2. 如果重启失败，重新部署
docker-compose down
docker-compose up -d

# 3. 检查数据完整性
psql -h localhost -U postgres -c "SELECT count(*) FROM users;"

# 4. 验证服务恢复
curl -f http://localhost:8000/health
```

### 4.2 数据丢失风险 (P0)

#### 4.2.1 立即响应
```bash
# 1. 停止所有写操作
docker-compose stop ai-assistant

# 2. 立即备份当前状态
pg_dump -h localhost -U postgres fitness_ai > emergency_backup_$(date +%Y%m%d_%H%M%S).sql

# 3. 评估数据损失范围
psql -h localhost -U postgres -c "SELECT * FROM pg_stat_database;"
```

#### 4.2.2 数据恢复
```bash
# 1. 从最近备份恢复
psql -h localhost -U postgres fitness_ai < /backups/latest_backup.sql

# 2. 验证数据完整性
# 运行数据一致性检查脚本

# 3. 重启应用服务
docker-compose start ai-assistant
```

## 5. 故障预防

### 5.1 监控告警设置
- 设置合理的告警阈值
- 配置多级告警通知
- 定期测试告警机制

### 5.2 定期健康检查
```bash
#!/bin/bash
# health_check.sh - 定期健康检查脚本

# 检查服务健康
curl -f http://localhost:8000/health || echo "服务健康检查失败"

# 检查数据库连接
pg_isready -h localhost -p 5432 || echo "数据库连接失败"

# 检查Redis连接
redis-cli ping || echo "Redis连接失败"

# 检查磁盘空间
df -h | awk '$5 > 80 {print "磁盘空间不足: " $0}'

# 检查内存使用
free | awk 'NR==2{printf "内存使用率: %.2f%%\n", $3*100/$2}'
```

### 5.3 容量规划
- 定期评估系统容量
- 监控资源使用趋势
- 提前规划扩容方案

## 6. 故障记录与分析

### 6.1 故障记录模板
```
故障ID: [自动生成]
发生时间: [YYYY-MM-DD HH:MM:SS]
故障级别: [P0/P1/P2/P3]
故障类型: [服务/数据库/网络/性能/安全]
影响范围: [描述受影响的功能和用户]
故障现象: [详细描述故障表现]
根本原因: [分析得出的根本原因]
解决方案: [采取的解决措施]
解决时间: [YYYY-MM-DD HH:MM:SS]
预防措施: [避免类似故障的措施]
```

### 6.2 故障分析流程
1. **故障复现**: 尝试重现故障场景
2. **日志分析**: 分析相关日志文件
3. **根因分析**: 使用5-Why分析法
4. **影响评估**: 评估故障影响范围
5. **改进建议**: 提出系统改进建议

## 7. 联系信息

### 7.1 紧急联系人
- **技术负责人**: [姓名] - [手机] - [邮箱]
- **运维负责人**: [姓名] - [手机] - [邮箱]
- **数据库管理员**: [姓名] - [手机] - [邮箱]

### 7.2 外部支持
- **云服务商支持**: [联系方式]
- **第三方服务支持**: [联系方式]
- **硬件供应商**: [联系方式]

---

**文档版本**: v1.0  
**最后更新**: 2024年1月  
**维护人员**: 技术团队
