# AI健身助手系统运维手册

## 1. 系统概述

### 1.1 系统架构
- **应用服务**: AI健身助手主服务 (3个实例)
- **数据库**: PostgreSQL 15 (主从复制)
- **缓存**: Redis 7 (集群模式)
- **负载均衡**: Nginx
- **监控**: Prometheus + Grafana + AlertManager
- **日志**: EL<PERSON> Stack
- **容器编排**: Docker Compose / Kubernetes

### 1.2 服务端口
| 服务 | 端口 | 协议 | 描述 |
|------|------|------|------|
| AI助手服务 | 8000 | HTTP | 主应用服务 |
| PostgreSQL | 5432 | TCP | 数据库服务 |
| Redis | 6379 | TCP | 缓存服务 |
| Nginx | 80/443 | HTTP/HTTPS | 负载均衡器 |
| Prometheus | 9090 | HTTP | 监控指标收集 |
| Grafana | 3000 | HTTP | 监控可视化 |
| AlertManager | 9093 | HTTP | 告警管理 |
| Elasticsearch | 9200 | HTTP | 日志存储 |
| Kibana | 5601 | HTTP | 日志可视化 |

## 2. 日常运维

### 2.1 健康检查

#### 2.1.1 服务健康检查
```bash
# 检查所有服务状态
docker-compose ps

# 检查AI助手服务健康
curl -f http://localhost:8000/health

# 检查数据库连接
pg_isready -h localhost -p 5432 -U postgres

# 检查Redis连接
redis-cli -h localhost -p 6379 ping

# 检查Nginx状态
curl -f http://localhost/health
```

#### 2.1.2 监控系统检查
```bash
# 检查Prometheus
curl -f http://localhost:9090/-/healthy

# 检查Grafana
curl -f http://localhost:3000/api/health

# 检查AlertManager
curl -f http://localhost:9093/-/healthy

# 检查Elasticsearch
curl -f http://localhost:9200/_cluster/health
```

### 2.2 日志管理

#### 2.2.1 日志查看
```bash
# 查看应用日志
docker-compose logs -f ai-assistant

# 查看数据库日志
docker-compose logs -f postgres

# 查看Redis日志
docker-compose logs -f redis

# 查看Nginx日志
docker-compose logs -f nginx

# 查看最近1小时的错误日志
docker-compose logs --since 1h ai-assistant | grep ERROR
```

#### 2.2.2 日志分析
```bash
# 统计错误日志数量
docker-compose logs ai-assistant | grep ERROR | wc -l

# 查看响应时间分布
grep "response_time" /app/logs/access.log | awk '{print $NF}' | sort -n

# 查看最频繁的错误
docker-compose logs ai-assistant | grep ERROR | sort | uniq -c | sort -nr
```

### 2.3 性能监控

#### 2.3.1 系统资源监控
```bash
# CPU使用率
top -p $(pgrep -f "uvicorn")

# 内存使用情况
free -h

# 磁盘使用情况
df -h

# 网络连接状态
netstat -tulpn | grep :8000
```

#### 2.3.2 应用性能监控
```bash
# 检查响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8000/health

# 检查数据库连接数
psql -h localhost -U postgres -c "SELECT count(*) FROM pg_stat_activity;"

# 检查Redis内存使用
redis-cli info memory | grep used_memory_human
```

## 3. 故障处理

### 3.1 常见故障及解决方案

#### 3.1.1 服务无响应
**症状**: 服务返回超时或连接拒绝
**排查步骤**:
1. 检查服务是否运行: `docker-compose ps`
2. 检查端口是否监听: `netstat -tulpn | grep 8000`
3. 检查资源使用: `top`, `free -h`, `df -h`
4. 查看错误日志: `docker-compose logs ai-assistant`

**解决方案**:
```bash
# 重启服务
docker-compose restart ai-assistant

# 如果资源不足，扩容
docker-compose up --scale ai-assistant=5

# 清理日志文件
find /app/logs -name "*.log" -mtime +7 -delete
```

#### 3.1.2 数据库连接失败
**症状**: 应用报数据库连接错误
**排查步骤**:
1. 检查数据库服务: `docker-compose ps postgres`
2. 检查连接: `pg_isready -h localhost -p 5432`
3. 检查连接数: `psql -c "SELECT count(*) FROM pg_stat_activity;"`

**解决方案**:
```bash
# 重启数据库
docker-compose restart postgres

# 检查并终止长时间运行的查询
psql -c "SELECT pid, query FROM pg_stat_activity WHERE state = 'active';"
psql -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE state = 'active' AND query_start < now() - interval '5 minutes';"
```

#### 3.1.3 缓存服务异常
**症状**: Redis连接失败或响应慢
**排查步骤**:
1. 检查Redis服务: `docker-compose ps redis`
2. 检查连接: `redis-cli ping`
3. 检查内存使用: `redis-cli info memory`

**解决方案**:
```bash
# 重启Redis
docker-compose restart redis

# 清理过期键
redis-cli --scan --pattern "*" | xargs redis-cli del

# 检查慢查询
redis-cli slowlog get 10
```

### 3.2 紧急故障处理

#### 3.2.1 系统完全不可用
```bash
# 1. 立即检查所有服务
docker-compose ps

# 2. 重启所有服务
docker-compose restart

# 3. 如果仍然失败，重新部署
docker-compose down
docker-compose up -d

# 4. 检查资源使用
df -h
free -h
top
```

#### 3.2.2 数据库故障恢复
```bash
# 1. 停止应用服务
docker-compose stop ai-assistant

# 2. 备份当前数据
pg_dump -h localhost -U postgres fitness_ai > backup_$(date +%Y%m%d_%H%M%S).sql

# 3. 从最近备份恢复
psql -h localhost -U postgres -c "DROP DATABASE fitness_ai;"
psql -h localhost -U postgres -c "CREATE DATABASE fitness_ai;"
psql -h localhost -U postgres fitness_ai < /backups/latest_backup.sql

# 4. 重启应用
docker-compose start ai-assistant
```

## 4. 备份与恢复

### 4.1 数据备份

#### 4.1.1 自动备份脚本
```bash
#!/bin/bash
# backup_database.sh

BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="fitness_ai"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
pg_dump -h localhost -U postgres $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/db_backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete

echo "数据库备份完成: $BACKUP_DIR/db_backup_$DATE.sql.gz"
```

#### 4.1.2 Redis备份
```bash
#!/bin/bash
# backup_redis.sh

BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建Redis备份
redis-cli BGSAVE
sleep 10  # 等待备份完成

# 复制RDB文件
cp /var/lib/redis/dump.rdb $BACKUP_DIR/redis_backup_$DATE.rdb

# 删除7天前的备份
find $BACKUP_DIR -name "redis_backup_*.rdb" -mtime +7 -delete

echo "Redis备份完成: $BACKUP_DIR/redis_backup_$DATE.rdb"
```

### 4.2 数据恢复

#### 4.2.1 数据库恢复
```bash
#!/bin/bash
# restore_database.sh

BACKUP_FILE=$1
DB_NAME="fitness_ai"

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <backup_file>"
    exit 1
fi

# 停止应用服务
docker-compose stop ai-assistant

# 删除现有数据库
psql -h localhost -U postgres -c "DROP DATABASE IF EXISTS $DB_NAME;"

# 创建新数据库
psql -h localhost -U postgres -c "CREATE DATABASE $DB_NAME;"

# 恢复数据
if [[ $BACKUP_FILE == *.gz ]]; then
    gunzip -c $BACKUP_FILE | psql -h localhost -U postgres $DB_NAME
else
    psql -h localhost -U postgres $DB_NAME < $BACKUP_FILE
fi

# 重启应用服务
docker-compose start ai-assistant

echo "数据库恢复完成"
```

## 5. 维护任务

### 5.1 定期维护

#### 5.1.1 每日任务
- [ ] 检查服务健康状态
- [ ] 查看错误日志
- [ ] 检查磁盘空间
- [ ] 验证备份完成

#### 5.1.2 每周任务
- [ ] 清理旧日志文件
- [ ] 检查数据库性能
- [ ] 更新系统补丁
- [ ] 验证监控告警

#### 5.1.3 每月任务
- [ ] 数据库维护（VACUUM、REINDEX）
- [ ] 性能基准测试
- [ ] 安全扫描
- [ ] 容量规划评估

### 5.2 维护脚本

#### 5.2.1 日志清理脚本
```bash
#!/bin/bash
# cleanup_logs.sh

LOG_DIR="/app/logs"
RETENTION_DAYS=7

# 清理应用日志
find $LOG_DIR -name "*.log" -mtime +$RETENTION_DAYS -delete

# 清理Docker日志
docker system prune -f

# 清理Elasticsearch旧索引
curl -X DELETE "localhost:9200/logstash-$(date -d '7 days ago' +%Y.%m.%d)"

echo "日志清理完成"
```

#### 5.2.2 数据库维护脚本
```bash
#!/bin/bash
# maintain_database.sh

DB_NAME="fitness_ai"

# 数据库统计信息更新
psql -h localhost -U postgres $DB_NAME -c "ANALYZE;"

# 清理死元组
psql -h localhost -U postgres $DB_NAME -c "VACUUM;"

# 重建索引
psql -h localhost -U postgres $DB_NAME -c "REINDEX DATABASE $DB_NAME;"

echo "数据库维护完成"
```

## 6. 监控告警

### 6.1 告警级别
- **Critical**: 服务不可用、数据丢失
- **Warning**: 性能下降、资源使用率高
- **Info**: 正常状态变化

### 6.2 告警处理流程
1. **接收告警**: 通过邮件/短信/钉钉接收
2. **初步评估**: 确定告警级别和影响范围
3. **故障定位**: 查看日志和监控数据
4. **应急处理**: 执行相应的故障处理步骤
5. **根因分析**: 分析故障原因
6. **预防措施**: 制定预防措施

### 6.3 联系方式
- **技术负责人**: [姓名] - [电话] - [邮箱]
- **运维负责人**: [姓名] - [电话] - [邮箱]
- **紧急联系人**: [姓名] - [电话] - [邮箱]

## 7. 安全管理

### 7.1 访问控制
- 定期更新密码
- 使用SSH密钥认证
- 限制管理员权限
- 审计访问日志

### 7.2 安全检查
```bash
# 检查开放端口
nmap -sT localhost

# 检查用户登录
last | head -20

# 检查系统更新
apt list --upgradable

# 检查文件权限
find /app -type f -perm 777
```

---

**文档版本**: v1.0  
**最后更新**: 2024年1月  
**维护人员**: 运维团队
