# 智能健身AI助手系统架构图

## 1. 整合后的系统架构

### 1.1 核心架构图

```mermaid
graph TB
    subgraph "API层"
        A[FastAPI Endpoints] --> B[WebSocket Handler]
        A --> C[REST API Handler]
    end
    
    subgraph "整合层 (app/services/ai_assistant/integration/)"
        D[IntegratedFitnessService] --> E[IntegratedStateAdapter]
        D --> F[IntegratedStateManager]
        D --> G[IntegratedIntentProcessor]
        D --> H[EnhancedParameterManager]
    end
    
    subgraph "LangGraph系统 (现有)"
        I[LangGraphService] --> J[StateGraph]
        J --> K[GraphNodes]
        K --> L[router_node]
        K --> M[param_collector_node]
        K --> N[expert_nodes]
    end
    
    subgraph "原始系统 (现有)"
        O[ConversationService] --> P[IntentRecognizer]
        O --> Q[TrainingParamManager]
        O --> R[UserProfileManager]
    end
    
    subgraph "数据层"
        S[(PostgreSQL)] --> T[Conversations]
        S --> U[Messages]
        S --> V[Users]
        S --> W[TrainingPlans]
        X[Redis Cache] --> Y[SessionState]
        X --> Z[ResponseCache]
    end
    
    A --> D
    D --> I
    D --> O
    E --> S
    E --> X
    F --> S
    F --> X
    G --> P
    H --> Q
    H --> R
```

### 1.2 文件依赖关系图

```mermaid
graph LR
    subgraph "Integration Layer"
        A[interfaces.py] --> B[state_adapter.py]
        A --> C[state_manager.py]
        A --> D[intent_processor.py]
        A --> E[parameter_manager.py]
    end
    
    subgraph "LangGraph System"
        F[langgraph_service.py] --> G[graph_nodes/]
        G --> H[router_node.py]
        G --> I[param_collector_node.py]
        G --> J[expert_nodes.py]
        K[state_definitions.py] --> F
    end
    
    subgraph "Legacy System"
        L[conversation/orchestrator.py] --> M[intent_recognizer.py]
        L --> N[training_param_manager.py]
        L --> O[user_profile_manager.py]
        P[parameter_extractor.py] --> N
    end
    
    B --> K
    B --> F
    B --> L
    C --> F
    C --> L
    D --> M
    D --> H
    E --> P
    E --> N
    E --> O
```

## 2. 数据流图

### 2.1 消息处理流程

```mermaid
sequenceDiagram
    participant U as User
    participant API as FastAPI
    participant IFS as IntegratedFitnessService
    participant ISA as IntegratedStateAdapter
    participant IIP as IntegratedIntentProcessor
    participant LGS as LangGraphService
    participant DB as Database
    
    U->>API: 发送消息
    API->>IFS: process_message()
    IFS->>ISA: convert_to_unified()
    ISA->>DB: 获取历史状态
    ISA-->>IFS: UnifiedFitnessState
    IFS->>IIP: process_intent()
    IIP->>LGS: 调用图节点
    LGS-->>IIP: 处理结果
    IIP-->>IFS: 更新状态
    IFS->>ISA: convert_from_unified()
    ISA->>DB: 保存状态
    IFS-->>API: 响应结果
    API-->>U: 返回响应
```

### 2.2 状态转换流程

```mermaid
stateDiagram-v2
    [*] --> LegacyState: 原始系统状态
    [*] --> LangGraphState: LangGraph状态
    [*] --> DictState: 字典状态
    
    LegacyState --> UnifiedState: StateAdapter.convert_to_unified()
    LangGraphState --> UnifiedState: StateAdapter.convert_to_unified()
    DictState --> UnifiedState: StateAdapter.convert_to_unified()
    
    UnifiedState --> Processing: IntegratedService.process()
    Processing --> UnifiedState: 处理完成
    
    UnifiedState --> LegacyState: StateAdapter.convert_from_unified()
    UnifiedState --> LangGraphState: StateAdapter.convert_from_unified()
    UnifiedState --> DictState: StateAdapter.convert_from_unified()
    
    LegacyState --> [*]
    LangGraphState --> [*]
    DictState --> [*]
```

## 3. 组件交互图

### 3.1 参数收集流程

```mermaid
graph TD
    A[用户消息] --> B[IntegratedIntentProcessor]
    B --> C{需要参数?}
    C -->|是| D[EnhancedParameterManager]
    C -->|否| E[直接处理]
    
    D --> F[检查现有参数]
    F --> G{参数完整?}
    G -->|否| H[调用param_collector_node]
    G -->|是| I[参数验证]
    
    H --> J[生成询问消息]
    J --> K[等待用户回复]
    K --> L[解析用户输入]
    L --> F
    
    I --> M{验证通过?}
    M -->|是| N[执行意图处理]
    M -->|否| O[重新收集]
    O --> H
    
    N --> P[返回结果]
    E --> P
```

### 3.2 意图处理流程

```mermaid
graph TD
    A[用户消息] --> B[IntegratedIntentProcessor]
    B --> C[提取上下文]
    C --> D[调用原始IntentRecognizer]
    D --> E{识别成功?}
    
    E -->|是| F[确定目标处理器]
    E -->|否| G[降级处理]
    
    F --> H{LangGraph节点?}
    H -->|是| I[调用对应图节点]
    H -->|否| J[调用原始处理器]
    
    I --> K[更新状态]
    J --> K
    G --> L[通用聊天处理]
    L --> K
    
    K --> M[记录处理路径]
    M --> N[返回结果]
```

## 4. 缓存架构

### 4.1 多层缓存设计

```mermaid
graph TB
    subgraph "L1 Cache (Memory)"
        A[状态缓存] --> B[TTL: 5分钟]
        C[响应缓存] --> D[TTL: 10分钟]
    end
    
    subgraph "L2 Cache (Redis)"
        E[会话状态] --> F[TTL: 1小时]
        G[用户信息] --> H[TTL: 24小时]
        I[训练参数] --> J[TTL: 6小时]
    end
    
    subgraph "L3 Storage (Database)"
        K[(Conversations)]
        L[(Messages)]
        M[(Users)]
        N[(TrainingPlans)]
    end
    
    A --> E
    C --> G
    E --> K
    G --> M
    I --> N
```

### 4.2 缓存策略

| 数据类型 | L1缓存 | L2缓存 | L3存储 | 更新策略 |
|---------|--------|--------|--------|----------|
| 会话状态 | 5分钟 | 1小时 | 永久 | Write-Through |
| 用户信息 | 10分钟 | 24小时 | 永久 | Write-Behind |
| 训练参数 | 5分钟 | 6小时 | 永久 | Write-Through |
| 响应内容 | 10分钟 | 30分钟 | 可选 | Cache-Aside |

## 5. 错误处理架构

### 5.1 错误分类和处理

```mermaid
graph TD
    A[错误发生] --> B[错误分类器]
    B --> C{错误类型}
    
    C -->|LLM错误| D[LLM错误处理]
    C -->|数据库错误| E[数据库错误处理]
    C -->|网络错误| F[网络错误处理]
    C -->|业务逻辑错误| G[业务错误处理]
    C -->|未知错误| H[通用错误处理]
    
    D --> I[重试机制]
    E --> J[连接池重置]
    F --> K[超时重试]
    G --> L[降级处理]
    H --> M[记录日志]
    
    I --> N{重试成功?}
    J --> N
    K --> N
    L --> O[返回默认响应]
    M --> O
    
    N -->|是| P[继续处理]
    N -->|否| Q[熔断器触发]
    Q --> R[服务降级]
    
    P --> S[正常响应]
    R --> T[降级响应]
```

### 5.2 熔断器设计

```mermaid
stateDiagram-v2
    [*] --> Closed: 初始状态
    Closed --> Open: 错误率超阈值
    Open --> HalfOpen: 超时后尝试
    HalfOpen --> Closed: 请求成功
    HalfOpen --> Open: 请求失败
    
    note right of Closed
        正常处理请求
        监控错误率
    end note
    
    note right of Open
        拒绝所有请求
        返回降级响应
    end note
    
    note right of HalfOpen
        允许少量请求
        测试服务恢复
    end note
```

## 6. 性能监控架构

### 6.1 监控指标体系

```mermaid
graph TB
    subgraph "业务指标"
        A[意图识别准确率]
        B[参数收集完成率]
        C[用户满意度]
    end
    
    subgraph "技术指标"
        D[响应时间]
        E[吞吐量]
        F[错误率]
        G[缓存命中率]
    end
    
    subgraph "系统指标"
        H[CPU使用率]
        I[内存使用率]
        J[数据库连接数]
        K[Redis连接数]
    end
    
    subgraph "监控工具"
        L[Prometheus]
        M[Grafana]
        N[ELK Stack]
    end
    
    A --> L
    B --> L
    C --> L
    D --> L
    E --> L
    F --> L
    G --> L
    H --> L
    I --> L
    J --> L
    K --> L
    
    L --> M
    L --> N
```

这个架构图展示了整合后系统的完整结构，包括组件间的依赖关系、数据流向、缓存策略和错误处理机制，为实施团队提供了清晰的技术蓝图。
