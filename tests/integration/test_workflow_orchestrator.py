"""
工作流编排器测试

测试统一业务流程管理和编排功能：
- 完整工作流执行
- 工作流阶段管理
- 错误处理和重试机制
- 工作流中断和恢复

验证工作流执行成功率>99%的目标。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from app.services.ai_assistant.integration.workflow_orchestrator import (
    WorkflowOrchestrator, WorkflowStage, WorkflowStatus
)
from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState


class TestWorkflowOrchestrator:
    """工作流编排器测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        return Mock()
    
    @pytest.fixture
    def mock_llm_service(self):
        """模拟LLM服务"""
        return Mock()
    
    @pytest.fixture
    def workflow_orchestrator(self, mock_db_session, mock_llm_service):
        """创建工作流编排器实例"""
        with patch.multiple(
            'app.services.ai_assistant.integration.workflow_orchestrator',
            IntegratedStateManager=Mock,
            IntegratedIntentProcessor=Mock,
            EnhancedParameterManager=Mock,
            StreamingProcessor=Mock
        ):
            orchestrator = WorkflowOrchestrator(mock_db_session, mock_llm_service)
            
            # 模拟组件
            orchestrator.state_manager = Mock()
            orchestrator.intent_processor = Mock()
            orchestrator.parameter_manager = Mock()
            orchestrator.streaming_processor = Mock()
            
            # 模拟异步方法
            orchestrator.state_manager.get_current_state = AsyncMock()
            orchestrator.state_manager.save_state = AsyncMock(return_value=True)
            orchestrator.intent_processor.process_intent = AsyncMock()
            orchestrator.parameter_manager.collect_user_info = AsyncMock()
            orchestrator.parameter_manager.collect_training_params = AsyncMock()
            
            return orchestrator
    
    @pytest.fixture
    def sample_state(self):
        """示例统一状态"""
        return UnifiedFitnessState(
            conversation_id="test_conv_001",
            user_id="test_user_001",
            session_id="test_session_001",
            timestamp=datetime.now(),
            
            intent="training_plan",
            confidence=0.9,
            intent_parameters={},
            
            user_profile={"age": 25, "gender": "male"},
            training_params={"body_part": "胸部", "scenario": "健身房"},
            fitness_goals=[],
            
            flow_state={},
            current_state_name="idle",
            current_node="",
            processing_system="workflow",
            
            response_content="",
            response_type="text",
            structured_data={},
            
            error_count=0,
            retry_count=0,
            processing_start_time=0.0,
            node_execution_times={},
            
            parallel_results=[],
            selected_result=None,
            
            messages=[]
        )
    
    @pytest.mark.asyncio
    async def test_execute_workflow_success(self, workflow_orchestrator, sample_state):
        """测试工作流执行成功"""
        # 模拟状态管理器返回状态
        workflow_orchestrator.state_manager.get_current_state.return_value = sample_state
        
        # 模拟意图处理器
        processed_state = sample_state.copy()
        processed_state["intent"] = "training_plan"
        processed_state["confidence"] = 0.9
        workflow_orchestrator.intent_processor.process_intent.return_value = processed_state
        
        # 模拟参数管理器
        param_state = processed_state.copy()
        param_state["flow_state"]["training_params_complete"] = True
        param_state["flow_state"]["user_info_complete"] = True
        workflow_orchestrator.parameter_manager.collect_training_params.return_value = param_state
        workflow_orchestrator.parameter_manager.collect_user_info.return_value = param_state
        
        # 执行工作流
        result = await workflow_orchestrator.execute_workflow(
            message="我想制定训练计划",
            conversation_id="test_conv_001",
            user_id="test_user_001"
        )
        
        # 验证结果
        assert result["conversation_id"] == "test_conv_001"
        assert result["intent"] == "training_plan"
        assert result["current_state_name"] == "completed"
        
        # 验证组件调用
        workflow_orchestrator.state_manager.get_current_state.assert_called_once()
        workflow_orchestrator.intent_processor.process_intent.assert_called_once()
        workflow_orchestrator.state_manager.save_state.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_stage_initialization(self, workflow_orchestrator):
        """测试初始化阶段"""
        workflow_info = {
            "conversation_id": "test_conv",
            "user_id": "test_user",
            "message": "测试消息"
        }
        
        # 模拟状态管理器
        initial_state = UnifiedFitnessState(
            conversation_id="test_conv",
            user_id="",
            session_id="test_conv",
            messages=[]
        )
        workflow_orchestrator.state_manager.get_current_state.return_value = initial_state
        
        result = await workflow_orchestrator._stage_initialization(workflow_info, None)
        
        # 验证初始化结果
        assert result["conversation_id"] == "test_conv"
        assert result["user_id"] == "test_user"
        assert result["session_id"] == "test_conv"
        assert len(result["messages"]) == 1
        assert result["messages"][0].content == "测试消息"
        assert result["current_state_name"] == "processing"
    
    @pytest.mark.asyncio
    async def test_stage_intent_recognition(self, workflow_orchestrator, sample_state):
        """测试意图识别阶段"""
        workflow_info = {"message": "我想制定训练计划"}
        
        # 模拟意图处理器
        processed_state = sample_state.copy()
        processed_state["intent"] = "training_plan"
        processed_state["confidence"] = 0.95
        workflow_orchestrator.intent_processor.process_intent.return_value = processed_state
        
        result = await workflow_orchestrator._stage_intent_recognition(workflow_info, sample_state)
        
        # 验证意图识别结果
        assert result["intent"] == "training_plan"
        assert result["confidence"] == 0.95
        
        # 验证调用
        workflow_orchestrator.intent_processor.process_intent.assert_called_once_with(
            "我想制定训练计划", sample_state
        )
    
    @pytest.mark.asyncio
    async def test_stage_parameter_collection_training(self, workflow_orchestrator, sample_state):
        """测试训练参数收集阶段"""
        # 设置训练计划意图
        sample_state["intent"] = "training_plan"
        
        # 模拟参数管理器
        param_state = sample_state.copy()
        param_state["flow_state"]["training_params_complete"] = True
        workflow_orchestrator.parameter_manager.collect_training_params.return_value = param_state
        workflow_orchestrator.parameter_manager.collect_user_info.return_value = param_state
        
        result = await workflow_orchestrator._stage_parameter_collection({}, sample_state)
        
        # 验证参数收集调用
        workflow_orchestrator.parameter_manager.collect_training_params.assert_called_once()
        workflow_orchestrator.parameter_manager.collect_user_info.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_stage_processing_complete_params(self, workflow_orchestrator, sample_state):
        """测试处理阶段 - 参数完整"""
        # 设置参数收集完成状态
        sample_state["intent"] = "training_plan"
        sample_state["flow_state"] = {
            "training_params_complete": True,
            "user_info_complete": True
        }
        
        result = await workflow_orchestrator._stage_processing({}, sample_state)
        
        # 验证处理结果
        assert "训练计划" in result["response_content"]
        assert result["response_type"] == "text"
        assert result["current_state_name"] == "processed"
        assert "plan_type" in result["structured_data"]
    
    @pytest.mark.asyncio
    async def test_stage_processing_collecting_params(self, workflow_orchestrator, sample_state):
        """测试处理阶段 - 仍在收集参数"""
        # 设置参数收集中状态
        sample_state["flow_state"] = {"collecting_training_params": True}
        
        result = await workflow_orchestrator._stage_processing({}, sample_state)
        
        # 验证跳过处理
        assert result["response_type"] == "parameter_collection"
    
    @pytest.mark.asyncio
    async def test_determine_next_stage_flow(self, workflow_orchestrator, sample_state):
        """测试工作流阶段流转"""
        workflow_info = {"stream": False}
        
        # 测试各阶段的流转
        next_stage = await workflow_orchestrator._determine_next_stage(
            WorkflowStage.INITIALIZATION, sample_state, workflow_info
        )
        assert next_stage == WorkflowStage.INTENT_RECOGNITION
        
        next_stage = await workflow_orchestrator._determine_next_stage(
            WorkflowStage.INTENT_RECOGNITION, sample_state, workflow_info
        )
        assert next_stage == WorkflowStage.PARAMETER_COLLECTION
        
        # 参数收集完成
        sample_state["flow_state"] = {"training_params_complete": True}
        next_stage = await workflow_orchestrator._determine_next_stage(
            WorkflowStage.PARAMETER_COLLECTION, sample_state, workflow_info
        )
        assert next_stage == WorkflowStage.PROCESSING
        
        next_stage = await workflow_orchestrator._determine_next_stage(
            WorkflowStage.PROCESSING, sample_state, workflow_info
        )
        assert next_stage == WorkflowStage.RESPONSE_GENERATION
        
        # 非流式模式
        next_stage = await workflow_orchestrator._determine_next_stage(
            WorkflowStage.RESPONSE_GENERATION, sample_state, workflow_info
        )
        assert next_stage == WorkflowStage.COMPLETION
    
    @pytest.mark.asyncio
    async def test_determine_next_stage_streaming(self, workflow_orchestrator, sample_state):
        """测试流式模式的阶段流转"""
        workflow_info = {"stream": True}
        
        next_stage = await workflow_orchestrator._determine_next_stage(
            WorkflowStage.RESPONSE_GENERATION, sample_state, workflow_info
        )
        assert next_stage == WorkflowStage.STREAMING
        
        next_stage = await workflow_orchestrator._determine_next_stage(
            WorkflowStage.STREAMING, sample_state, workflow_info
        )
        assert next_stage == WorkflowStage.COMPLETION
    
    @pytest.mark.asyncio
    async def test_workflow_error_handling(self, workflow_orchestrator):
        """测试工作流错误处理"""
        # 模拟状态管理器抛出异常
        workflow_orchestrator.state_manager.get_current_state.side_effect = Exception("数据库错误")
        
        result = await workflow_orchestrator.execute_workflow(
            message="测试消息",
            conversation_id="test_conv_error"
        )
        
        # 验证错误处理
        assert result["conversation_id"] == "test_conv_error"
        assert result["response_type"] == "error"
        assert result["error_count"] >= 1
        assert "问题" in result["response_content"]
    
    @pytest.mark.asyncio
    async def test_workflow_retry_mechanism(self, workflow_orchestrator, sample_state):
        """测试工作流重试机制"""
        # 模拟状态管理器
        workflow_orchestrator.state_manager.get_current_state.return_value = sample_state
        
        # 模拟意图处理器前两次失败，第三次成功
        call_count = 0
        def mock_process_intent(message, state):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise Exception("临时错误")
            else:
                result_state = state.copy()
                result_state["intent"] = "training_plan"
                return result_state
        
        workflow_orchestrator.intent_processor.process_intent.side_effect = mock_process_intent
        
        # 模拟参数管理器
        workflow_orchestrator.parameter_manager.collect_training_params.return_value = sample_state
        workflow_orchestrator.parameter_manager.collect_user_info.return_value = sample_state
        
        # 设置较小的重试次数用于测试
        workflow_orchestrator.max_retry_attempts = 3
        
        result = await workflow_orchestrator.execute_workflow(
            message="测试重试",
            conversation_id="test_retry"
        )
        
        # 验证重试成功
        assert result["intent"] == "training_plan"
        assert call_count == 3  # 重试了3次
    
    @pytest.mark.asyncio
    async def test_workflow_timeout(self, workflow_orchestrator, sample_state):
        """测试工作流超时"""
        # 设置较短的超时时间
        workflow_orchestrator.max_workflow_time = 0.1  # 100ms
        
        # 模拟状态管理器
        workflow_orchestrator.state_manager.get_current_state.return_value = sample_state
        
        # 模拟慢速意图处理器
        async def slow_process_intent(message, state):
            await asyncio.sleep(0.2)  # 200ms延迟
            return state
        
        workflow_orchestrator.intent_processor.process_intent.side_effect = slow_process_intent
        
        # 执行工作流（应该超时）
        result = await workflow_orchestrator.execute_workflow(
            message="测试超时",
            conversation_id="test_timeout"
        )
        
        # 验证超时处理
        assert result["response_type"] == "error"
        assert "问题" in result["response_content"]
    
    @pytest.mark.asyncio
    async def test_interrupt_workflow(self, workflow_orchestrator):
        """测试工作流中断"""
        conversation_id = "test_interrupt"
        
        # 添加活跃工作流
        workflow_orchestrator.active_workflows[conversation_id] = {
            "workflow_id": f"{conversation_id}_123",
            "status": WorkflowStatus.RUNNING
        }
        
        success = await workflow_orchestrator.interrupt_workflow(conversation_id)
        
        assert success is True
        assert workflow_orchestrator.active_workflows[conversation_id]["status"] == WorkflowStatus.INTERRUPTED
    
    def test_get_workflow_status(self, workflow_orchestrator):
        """测试获取工作流状态"""
        conversation_id = "test_status"
        workflow_info = {
            "workflow_id": f"{conversation_id}_123",
            "status": WorkflowStatus.RUNNING,
            "current_stage": WorkflowStage.PROCESSING
        }
        
        workflow_orchestrator.active_workflows[conversation_id] = workflow_info
        
        status = workflow_orchestrator.get_workflow_status(conversation_id)
        
        assert status == workflow_info
        assert status["status"] == WorkflowStatus.RUNNING
        assert status["current_stage"] == WorkflowStage.PROCESSING
    
    def test_get_active_workflows(self, workflow_orchestrator):
        """测试获取活跃工作流"""
        workflow_orchestrator.active_workflows = {
            "conv1": {"status": WorkflowStatus.RUNNING},
            "conv2": {"status": WorkflowStatus.RUNNING}
        }
        
        active_workflows = workflow_orchestrator.get_active_workflows()
        
        assert len(active_workflows) == 2
        assert "conv1" in active_workflows
        assert "conv2" in active_workflows
    
    @pytest.mark.asyncio
    async def test_performance_workflow_execution(self, workflow_orchestrator, sample_state):
        """性能测试 - 工作流执行"""
        # 模拟快速组件
        workflow_orchestrator.state_manager.get_current_state.return_value = sample_state
        
        processed_state = sample_state.copy()
        processed_state["intent"] = "training_plan"
        processed_state["flow_state"] = {"training_params_complete": True, "user_info_complete": True}
        
        workflow_orchestrator.intent_processor.process_intent.return_value = processed_state
        workflow_orchestrator.parameter_manager.collect_training_params.return_value = processed_state
        workflow_orchestrator.parameter_manager.collect_user_info.return_value = processed_state
        
        # 测试多次执行的平均时间
        times = []
        for i in range(20):
            start_time = asyncio.get_event_loop().time()
            
            await workflow_orchestrator.execute_workflow(
                message=f"测试消息 {i}",
                conversation_id=f"perf_test_{i}"
            )
            
            end_time = asyncio.get_event_loop().time()
            times.append(end_time - start_time)
        
        avg_time = sum(times) / len(times)
        
        # 验证平均执行时间 < 100ms
        assert avg_time < 0.1, f"工作流平均执行时间过长: {avg_time:.4f}s"
    
    @pytest.mark.asyncio
    async def test_concurrent_workflow_execution(self, workflow_orchestrator, sample_state):
        """并发测试 - 工作流执行"""
        # 模拟组件
        workflow_orchestrator.state_manager.get_current_state.return_value = sample_state
        
        processed_state = sample_state.copy()
        processed_state["intent"] = "training_plan"
        processed_state["flow_state"] = {"training_params_complete": True, "user_info_complete": True}
        
        workflow_orchestrator.intent_processor.process_intent.return_value = processed_state
        workflow_orchestrator.parameter_manager.collect_training_params.return_value = processed_state
        workflow_orchestrator.parameter_manager.collect_user_info.return_value = processed_state
        
        # 创建并发任务
        tasks = []
        for i in range(10):
            task = workflow_orchestrator.execute_workflow(
                message=f"并发消息 {i}",
                conversation_id=f"concurrent_test_{i}"
            )
            tasks.append(task)
        
        # 执行并发工作流
        results = await asyncio.gather(*tasks)
        
        # 验证所有工作流都成功完成
        assert len(results) == 10
        assert all(result["current_state_name"] == "completed" for result in results)
        assert all(result["intent"] == "training_plan" for result in results)
