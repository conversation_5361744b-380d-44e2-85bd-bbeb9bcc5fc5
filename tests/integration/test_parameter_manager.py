"""
增强参数管理器测试

测试参数收集和验证功能：
- 用户信息收集流程
- 训练参数收集流程
- 参数验证和错误处理
- 超时和降级处理

验证参数收集完整性100%的目标。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from app.services.ai_assistant.integration.parameter_manager import EnhancedParameterManager, ParameterValidator
from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState


class TestParameterValidator:
    """参数验证器测试类"""
    
    @pytest.fixture
    def validator(self):
        """创建参数验证器实例"""
        return ParameterValidator()
    
    def test_validate_user_profile_success(self, validator):
        """测试用户档案验证成功"""
        profile = {
            "age": 25,
            "gender": "male",
            "height": 175,
            "weight": 70,
            "fitness_level": "intermediate"
        }
        
        result = validator.validate_user_profile(profile)
        
        assert result["age"] == 25
        assert result["gender"] == "male"
        assert result["height"] == 175
        assert result["weight"] == 70
        assert result["fitness_level"] == "intermediate"
    
    def test_validate_user_profile_type_conversion(self, validator):
        """测试用户档案类型转换"""
        profile = {
            "age": "25",  # 字符串转整数
            "gender": "male",
            "height": "175.5",  # 字符串转浮点数
            "weight": 70
        }
        
        result = validator.validate_user_profile(profile)
        
        assert result["age"] == 25
        assert isinstance(result["age"], int)
        assert result["height"] == 175.5
        assert isinstance(result["height"], float)
    
    def test_validate_user_profile_missing_required(self, validator):
        """测试用户档案缺少必需字段"""
        profile = {
            "age": 25,
            "gender": "male"
            # 缺少height和weight
        }
        
        with pytest.raises(ValueError) as exc_info:
            validator.validate_user_profile(profile)
        
        assert "height为必需字段" in str(exc_info.value)
        assert "weight为必需字段" in str(exc_info.value)
    
    def test_validate_training_params_success(self, validator):
        """测试训练参数验证成功"""
        params = {
            "body_part": "胸部",
            "scenario": "健身房",
            "training_days": 3,
            "available_time": 60
        }
        
        result = validator.validate_training_params(params)
        
        assert result["body_part"] == "胸部"
        assert result["scenario"] == "健身房"
        assert result["training_days"] == 3
        assert result["available_time"] == 60
    
    def test_validate_training_params_invalid_choice(self, validator):
        """测试训练参数无效选择"""
        params = {
            "body_part": "无效部位",  # 不在选择列表中
            "scenario": "健身房"
        }
        
        with pytest.raises(ValueError) as exc_info:
            validator.validate_training_params(params)
        
        assert "body_part必须为" in str(exc_info.value)


class TestEnhancedParameterManager:
    """增强参数管理器测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        return Mock()
    
    @pytest.fixture
    def mock_llm_service(self):
        """模拟LLM服务"""
        return Mock()
    
    @pytest.fixture
    def parameter_manager(self, mock_db_session, mock_llm_service):
        """创建参数管理器实例"""
        return EnhancedParameterManager(mock_db_session, mock_llm_service)
    
    @pytest.fixture
    def sample_state(self):
        """示例统一状态"""
        return UnifiedFitnessState(
            conversation_id="test_conv_001",
            user_id="test_user_001",
            session_id="test_session_001",
            timestamp=datetime.now(),
            
            intent="training_plan",
            confidence=0.9,
            intent_parameters={},
            
            user_profile={},
            training_params={},
            fitness_goals=[],
            
            flow_state={},
            current_state_name="idle",
            current_node="",
            processing_system="test",
            
            response_content="",
            response_type="text",
            structured_data={},
            
            error_count=0,
            retry_count=0,
            processing_start_time=0.0,
            node_execution_times={},
            
            parallel_results=[],
            selected_result=None,
            
            messages=[]
        )
    
    @pytest.mark.asyncio
    async def test_collect_user_info_complete(self, parameter_manager, sample_state):
        """测试用户信息已完整的情况"""
        # 设置完整的用户信息
        sample_state["user_profile"] = {
            "age": 25,
            "gender": "male",
            "height": 175,
            "weight": 70
        }
        
        # 模拟管理器方法
        parameter_manager.user_profile_manager.check_missing_fields = AsyncMock(return_value=[])
        
        result = await parameter_manager.collect_user_info(sample_state)
        
        assert result["flow_state"]["user_info_complete"] is True
        assert "用户信息已完整" in result["response_content"]
    
    @pytest.mark.asyncio
    async def test_collect_user_info_missing_fields(self, parameter_manager, sample_state):
        """测试用户信息缺失的情况"""
        # 设置不完整的用户信息
        sample_state["user_profile"] = {"age": 25}
        
        # 模拟管理器方法
        parameter_manager.user_profile_manager.check_missing_fields = AsyncMock(
            return_value=["gender", "height", "weight"]
        )
        parameter_manager.user_profile_manager.generate_collection_prompt = AsyncMock(
            return_value="请告诉我您的性别"
        )
        
        result = await parameter_manager.collect_user_info(sample_state)
        
        assert result["flow_state"]["collecting_user_info"] is True
        assert result["flow_state"]["current_field"] == "gender"
        assert "请告诉我您的性别" in result["response_content"]
    
    @pytest.mark.asyncio
    async def test_collect_training_params_success(self, parameter_manager, sample_state):
        """测试训练参数收集成功"""
        # 添加用户消息
        from app.services.state_definitions import AnyMessage
        sample_state["messages"] = [
            AnyMessage(role="user", content="我想练胸部，在健身房训练")
        ]
        
        # 模拟参数提取器
        parameter_manager.parameter_extractor.extract_parameters = AsyncMock(
            return_value={"body_part": "胸部", "scenario": "健身房"}
        )
        
        # 模拟训练参数管理器
        parameter_manager.training_param_manager.check_missing_params = AsyncMock(
            return_value=[]
        )
        
        result = await parameter_manager.collect_training_params(sample_state)
        
        assert result["training_params"]["body_part"] == "胸部"
        assert result["training_params"]["scenario"] == "健身房"
        assert result["flow_state"]["training_params_complete"] is True
    
    @pytest.mark.asyncio
    async def test_collect_training_params_missing(self, parameter_manager, sample_state):
        """测试训练参数缺失的情况"""
        # 添加用户消息
        from app.services.state_definitions import AnyMessage
        sample_state["messages"] = [
            AnyMessage(role="user", content="我想制定训练计划")
        ]
        
        # 模拟参数提取器
        parameter_manager.parameter_extractor.extract_parameters = AsyncMock(
            return_value={}
        )
        
        # 模拟训练参数管理器
        parameter_manager.training_param_manager.check_missing_params = AsyncMock(
            return_value=["body_part", "scenario"]
        )
        parameter_manager.training_param_manager.generate_param_prompt = AsyncMock(
            return_value="您想训练哪个部位？"
        )
        
        result = await parameter_manager.collect_training_params(sample_state)
        
        assert result["flow_state"]["collecting_training_params"] is True
        assert result["flow_state"]["current_param"] == "body_part"
        assert "您想训练哪个部位？" in result["response_content"]
    
    @pytest.mark.asyncio
    async def test_validate_parameters_training_plan(self, parameter_manager):
        """测试训练计划参数验证"""
        params = {
            "body_part": "胸部",
            "scenario": "健身房",
            "training_days": 3
        }
        
        result = await parameter_manager.validate_parameters(params, "training_plan")
        
        assert result["body_part"] == "胸部"
        assert result["scenario"] == "健身房"
        assert result["training_days"] == 3
    
    @pytest.mark.asyncio
    async def test_continue_user_info_collection_success(self, parameter_manager, sample_state):
        """测试继续用户信息收集成功"""
        # 设置收集状态
        sample_state["flow_state"] = {
            "collecting_user_info": True,
            "current_field": "age",
            "missing_fields": ["age", "gender"],
            "collection_start_time": time.time(),
            "collection_round": 1
        }
        
        # 添加用户回复
        from app.services.state_definitions import AnyMessage
        sample_state["messages"] = [
            AnyMessage(role="user", content="我25岁")
        ]
        
        # 模拟用户信息提取
        parameter_manager.user_profile_manager.extract_user_info = AsyncMock(
            return_value=25
        )
        parameter_manager.user_profile_manager.generate_collection_prompt = AsyncMock(
            return_value="请告诉我您的性别"
        )
        
        result = await parameter_manager._continue_user_info_collection(sample_state)
        
        assert result["user_profile"]["age"] == 25
        assert result["flow_state"]["current_field"] == "gender"
        assert "请告诉我您的性别" in result["response_content"]
    
    @pytest.mark.asyncio
    async def test_continue_user_info_collection_complete(self, parameter_manager, sample_state):
        """测试用户信息收集完成"""
        # 设置收集状态（最后一个字段）
        sample_state["flow_state"] = {
            "collecting_user_info": True,
            "current_field": "weight",
            "missing_fields": ["weight"],
            "collection_start_time": time.time(),
            "collection_round": 1
        }
        
        # 添加用户回复
        from app.services.state_definitions import AnyMessage
        sample_state["messages"] = [
            AnyMessage(role="user", content="我70公斤")
        ]
        
        # 模拟用户信息提取
        parameter_manager.user_profile_manager.extract_user_info = AsyncMock(
            return_value=70.0
        )
        
        result = await parameter_manager._continue_user_info_collection(sample_state)
        
        assert result["user_profile"]["weight"] == 70.0
        assert result["flow_state"]["collecting_user_info"] is False
        assert result["flow_state"]["user_info_complete"] is True
        assert "用户信息收集完成" in result["response_content"]
    
    @pytest.mark.asyncio
    async def test_timeout_handling(self, parameter_manager, sample_state):
        """测试超时处理"""
        import time
        
        # 设置超时的收集状态
        sample_state["flow_state"] = {
            "collecting_user_info": True,
            "current_field": "age",
            "missing_fields": ["age"],
            "collection_start_time": time.time() - 400,  # 超时
            "collection_round": 1
        }
        
        result = await parameter_manager._continue_user_info_collection(sample_state)
        
        assert result["flow_state"]["collecting_user_info"] is False
        assert "超时" in result["response_content"]
    
    @pytest.mark.asyncio
    async def test_max_retry_handling(self, parameter_manager, sample_state):
        """测试最大重试次数处理"""
        # 设置达到最大重试次数的状态
        sample_state["flow_state"] = {
            "collecting_user_info": True,
            "current_field": "age",
            "missing_fields": ["age"],
            "collection_start_time": time.time(),
            "collection_round": 6  # 超过最大重试次数
        }
        
        # 添加无效回复
        from app.services.state_definitions import AnyMessage
        sample_state["messages"] = [
            AnyMessage(role="user", content="不知道")
        ]
        
        # 模拟提取失败
        parameter_manager.user_profile_manager.extract_user_info = AsyncMock(
            return_value=None
        )
        
        result = await parameter_manager._continue_user_info_collection(sample_state)
        
        assert result["flow_state"]["collecting_user_info"] is False
        assert "超时" in result["response_content"]
    
    @pytest.mark.asyncio
    async def test_error_handling(self, parameter_manager, sample_state):
        """测试错误处理"""
        # 模拟管理器方法抛出异常
        parameter_manager.user_profile_manager.check_missing_fields = AsyncMock(
            side_effect=Exception("测试异常")
        )
        
        result = await parameter_manager.collect_user_info(sample_state)
        
        assert result["flow_state"]["collecting_user_info"] is False
        assert result["error_count"] == 1
        assert "遇到问题" in result["response_content"]
    
    @pytest.mark.asyncio
    async def test_performance_parameter_collection(self, parameter_manager, sample_state):
        """性能测试 - 参数收集"""
        # 设置完整参数
        sample_state["user_profile"] = {
            "age": 25, "gender": "male", "height": 175, "weight": 70
        }
        sample_state["training_params"] = {
            "body_part": "胸部", "scenario": "健身房"
        }
        
        # 模拟管理器方法
        parameter_manager.user_profile_manager.check_missing_fields = AsyncMock(return_value=[])
        parameter_manager.training_param_manager.check_missing_params = AsyncMock(return_value=[])
        
        # 测试多次收集的平均时间
        times = []
        for _ in range(50):
            start_time = asyncio.get_event_loop().time()
            await parameter_manager.collect_user_info(sample_state)
            await parameter_manager.collect_training_params(sample_state)
            end_time = asyncio.get_event_loop().time()
            times.append(end_time - start_time)
        
        avg_time = sum(times) / len(times)
        
        # 验证平均处理时间 < 50ms
        assert avg_time < 0.05, f"参数收集平均时间过长: {avg_time:.4f}s"
