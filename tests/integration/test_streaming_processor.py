"""
流式处理管理器测试

测试流式响应处理功能：
- 流式响应生成和处理
- WebSocket连接管理
- 流式响应中断和恢复
- 性能和延迟测试

验证流式响应延迟<500ms的目标。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from app.services.ai_assistant.integration.streaming_processor import StreamingProcessor, WebSocketStreamingHandler
from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState


class TestStreamingProcessor:
    """流式处理管理器测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        return Mock()
    
    @pytest.fixture
    def mock_llm_service(self):
        """模拟LLM服务"""
        return Mock()
    
    @pytest.fixture
    def streaming_processor(self, mock_db_session, mock_llm_service):
        """创建流式处理器实例"""
        with patch('app.services.ai_assistant.integration.streaming_processor.LangGraphService'):
            processor = StreamingProcessor(mock_db_session, mock_llm_service)
            
            # 模拟LangGraph服务
            processor.langgraph_service = Mock()
            processor.langgraph_service.graph = Mock()
            processor.langgraph_service.graph.astream = AsyncMock()
            
            return processor
    
    @pytest.fixture
    def sample_state(self):
        """示例统一状态"""
        return UnifiedFitnessState(
            conversation_id="test_conv_001",
            user_id="test_user_001",
            session_id="test_session_001",
            timestamp=datetime.now(),
            
            intent="training_plan",
            confidence=0.9,
            intent_parameters={},
            
            user_profile={"age": 25, "gender": "male"},
            training_params={"body_part": "胸部", "scenario": "健身房"},
            fitness_goals=[],
            
            flow_state={},
            current_state_name="processing",
            current_node="",
            processing_system="streaming",
            
            response_content="正在为您制定训练计划...",
            response_type="text",
            structured_data={},
            
            error_count=0,
            retry_count=0,
            processing_start_time=0.0,
            node_execution_times={},
            
            parallel_results=[],
            selected_result=None,
            
            messages=[]
        )
    
    @pytest.mark.asyncio
    async def test_stream_response_success(self, streaming_processor, sample_state):
        """测试流式响应成功"""
        session_id = sample_state["session_id"]
        
        # 模拟流式响应数据
        mock_chunks = [
            {"response_content": "正在分析您的需求..."},
            {"response_content": "正在制定训练计划..."},
            {"response_content": "训练计划制定完成！"},
            {"current_node": "training_plan_expert"},
            {"structured_data": {"plan": "胸部训练计划"}}
        ]
        
        streaming_processor.langgraph_service.graph.astream.return_value = mock_chunks.__iter__()
        
        # 收集流式响应
        chunks = []
        async for chunk in streaming_processor.stream_response(sample_state):
            chunks.append(chunk)
        
        # 验证响应结构
        assert len(chunks) >= 3  # 至少包含开始、内容、结束信号
        
        # 验证开始信号
        start_chunk = chunks[0]
        assert start_chunk["type"] == "stream_start"
        assert start_chunk["session_id"] == session_id
        
        # 验证内容块
        content_chunks = [chunk for chunk in chunks if chunk["type"] == "content"]
        assert len(content_chunks) >= 3
        
        # 验证结束信号
        end_chunk = chunks[-1]
        assert end_chunk["type"] == "stream_end"
        assert end_chunk["session_id"] == session_id
        assert "chunks_sent" in end_chunk
        assert "duration" in end_chunk
    
    @pytest.mark.asyncio
    async def test_stream_response_interruption(self, streaming_processor, sample_state):
        """测试流式响应中断"""
        session_id = sample_state["session_id"]
        
        # 模拟长时间流式响应
        async def mock_long_stream():
            for i in range(100):
                yield {"response_content": f"处理步骤 {i}..."}
                await asyncio.sleep(0.01)  # 模拟处理时间
        
        streaming_processor.langgraph_service.graph.astream.return_value = mock_long_stream()
        
        # 启动流式响应
        stream_task = asyncio.create_task(
            self._collect_stream_chunks(streaming_processor.stream_response(sample_state))
        )
        
        # 等待一段时间后中断
        await asyncio.sleep(0.1)
        streaming_processor.interrupt_stream(session_id)
        
        # 等待流式响应完成
        chunks = await stream_task
        
        # 验证中断效果
        assert len(chunks) < 100  # 应该被中断，不会收到所有块
        
        # 验证流式会话状态
        stream_status = streaming_processor.get_stream_status(session_id)
        assert stream_status is None  # 会话应该被清理
    
    async def _collect_stream_chunks(self, stream):
        """收集流式响应块"""
        chunks = []
        async for chunk in stream:
            chunks.append(chunk)
        return chunks
    
    @pytest.mark.asyncio
    async def test_handle_interruption(self, streaming_processor, sample_state):
        """测试处理流式中断"""
        session_id = sample_state["session_id"]
        
        # 添加活跃流式会话
        streaming_processor.active_streams[session_id] = {
            "session_id": session_id,
            "status": "active",
            "start_time": asyncio.get_event_loop().time()
        }
        
        result = await streaming_processor.handle_interruption(sample_state)
        
        # 验证中断处理结果
        assert result["flow_state"]["stream_interrupted"] is True
        assert "interrupt_time" in result["flow_state"]
        assert result["response_type"] == "interruption"
        assert "中断" in result["response_content"]
        
        # 验证流式会话状态更新
        stream_info = streaming_processor.active_streams[session_id]
        assert stream_info["status"] == "interrupted"
    
    @pytest.mark.asyncio
    async def test_process_stream_chunk_content(self, streaming_processor):
        """测试处理内容流式块"""
        chunk = {"response_content": "这是测试内容"}
        session_id = "test_session"
        
        result = await streaming_processor._process_stream_chunk(chunk, session_id)
        
        assert result["type"] == "content"
        assert result["session_id"] == session_id
        assert result["content"] == "这是测试内容"
        assert "timestamp" in result
    
    @pytest.mark.asyncio
    async def test_process_stream_chunk_node_update(self, streaming_processor):
        """测试处理节点更新流式块"""
        chunk = {"current_node": "training_plan_expert"}
        session_id = "test_session"
        
        result = await streaming_processor._process_stream_chunk(chunk, session_id)
        
        assert result["type"] == "node_update"
        assert result["session_id"] == session_id
        assert result["current_node"] == "training_plan_expert"
    
    @pytest.mark.asyncio
    async def test_process_stream_chunk_structured_data(self, streaming_processor):
        """测试处理结构化数据流式块"""
        chunk = {"structured_data": {"plan": "胸部训练", "exercises": ["卧推", "飞鸟"]}}
        session_id = "test_session"
        
        result = await streaming_processor._process_stream_chunk(chunk, session_id)
        
        assert result["type"] == "structured_data"
        assert result["session_id"] == session_id
        assert result["data"]["plan"] == "胸部训练"
        assert len(result["data"]["exercises"]) == 2
    
    @pytest.mark.asyncio
    async def test_process_stream_chunk_text(self, streaming_processor):
        """测试处理文本流式块"""
        chunk = "这是纯文本内容"
        session_id = "test_session"
        
        result = await streaming_processor._process_stream_chunk(chunk, session_id)
        
        assert result["type"] == "text"
        assert result["session_id"] == session_id
        assert result["content"] == "这是纯文本内容"
    
    @pytest.mark.asyncio
    async def test_cleanup_expired_streams(self, streaming_processor):
        """测试清理过期流式会话"""
        import time
        
        # 添加正常和过期的流式会话
        current_time = time.time()
        streaming_processor.active_streams = {
            "active_session": {
                "session_id": "active_session",
                "start_time": current_time - 10,  # 10秒前开始
                "status": "active"
            },
            "expired_session": {
                "session_id": "expired_session", 
                "start_time": current_time - 100,  # 100秒前开始（超过2倍超时时间）
                "status": "active"
            }
        }
        
        cleaned_count = await streaming_processor.cleanup_expired_streams()
        
        assert cleaned_count == 1
        assert "active_session" in streaming_processor.active_streams
        assert "expired_session" not in streaming_processor.active_streams
    
    @pytest.mark.asyncio
    async def test_stream_timeout(self, streaming_processor, sample_state):
        """测试流式响应超时"""
        # 设置较短的超时时间
        streaming_processor.max_response_time = 0.1  # 100ms
        
        # 模拟慢速流式响应
        async def mock_slow_stream():
            for i in range(10):
                yield {"response_content": f"慢速处理 {i}..."}
                await asyncio.sleep(0.05)  # 每块50ms，总共500ms
        
        streaming_processor.langgraph_service.graph.astream.return_value = mock_slow_stream()
        
        chunks = []
        async for chunk in streaming_processor.stream_response(sample_state):
            chunks.append(chunk)
        
        # 验证超时处理
        timeout_chunks = [chunk for chunk in chunks if chunk["type"] == "timeout"]
        assert len(timeout_chunks) > 0
        
        timeout_chunk = timeout_chunks[0]
        assert "超时" in timeout_chunk["message"]
    
    @pytest.mark.asyncio
    async def test_performance_stream_processing(self, streaming_processor, sample_state):
        """性能测试 - 流式处理"""
        # 模拟快速流式响应
        mock_chunks = [
            {"response_content": f"快速处理 {i}..."} for i in range(100)
        ]
        
        streaming_processor.langgraph_service.graph.astream.return_value = mock_chunks.__iter__()
        
        start_time = asyncio.get_event_loop().time()
        
        chunks = []
        async for chunk in streaming_processor.stream_response(sample_state):
            chunks.append(chunk)
        
        end_time = asyncio.get_event_loop().time()
        processing_time = end_time - start_time
        
        # 验证处理时间 < 500ms
        assert processing_time < 0.5, f"流式处理时间过长: {processing_time:.4f}s"
        
        # 验证所有块都被处理
        content_chunks = [chunk for chunk in chunks if chunk["type"] == "content"]
        assert len(content_chunks) == 100


class TestWebSocketStreamingHandler:
    """WebSocket流式处理器测试类"""
    
    @pytest.fixture
    def mock_streaming_processor(self):
        """模拟流式处理器"""
        processor = Mock()
        processor.stream_response = AsyncMock()
        processor.interrupt_stream = Mock(return_value=True)
        processor.state_adapter = Mock()
        processor.state_adapter.convert_to_unified = AsyncMock()
        return processor
    
    @pytest.fixture
    def websocket_handler(self, mock_streaming_processor):
        """创建WebSocket处理器实例"""
        return WebSocketStreamingHandler(mock_streaming_processor)
    
    @pytest.fixture
    def mock_websocket(self):
        """模拟WebSocket连接"""
        websocket = Mock()
        websocket.send_json = AsyncMock()
        websocket.receive_json = AsyncMock()
        return websocket
    
    @pytest.mark.asyncio
    async def test_handle_websocket_message_start_stream(self, websocket_handler, mock_websocket):
        """测试处理开始流式消息"""
        session_id = "test_session"
        message = {
            "type": "start_stream",
            "state": {"conversation_id": "test_conv", "intent": "training_plan"}
        }
        
        # 模拟流式响应
        mock_chunks = [
            {"type": "content", "content": "测试内容"},
            {"type": "stream_end", "session_id": session_id}
        ]
        
        async def mock_stream_response(state):
            for chunk in mock_chunks:
                yield chunk
        
        websocket_handler.streaming_processor.stream_response.side_effect = mock_stream_response
        
        await websocket_handler._handle_websocket_message(mock_websocket, session_id, message)
        
        # 验证WebSocket发送了流式响应
        assert mock_websocket.send_json.call_count == len(mock_chunks)
    
    @pytest.mark.asyncio
    async def test_handle_websocket_message_interrupt(self, websocket_handler, mock_websocket):
        """测试处理中断流式消息"""
        session_id = "test_session"
        message = {"type": "interrupt_stream"}
        
        await websocket_handler._handle_websocket_message(mock_websocket, session_id, message)
        
        # 验证中断调用
        websocket_handler.streaming_processor.interrupt_stream.assert_called_once_with(session_id)
        
        # 验证发送中断确认
        mock_websocket.send_json.assert_called_once()
        sent_message = mock_websocket.send_json.call_args[0][0]
        assert sent_message["type"] == "stream_interrupted"
    
    @pytest.mark.asyncio
    async def test_handle_websocket_message_ping(self, websocket_handler, mock_websocket):
        """测试处理心跳消息"""
        session_id = "test_session"
        message = {"type": "ping"}
        
        await websocket_handler._handle_websocket_message(mock_websocket, session_id, message)
        
        # 验证发送pong响应
        mock_websocket.send_json.assert_called_once()
        sent_message = mock_websocket.send_json.call_args[0][0]
        assert sent_message["type"] == "pong"
        assert sent_message["session_id"] == session_id
    
    @pytest.mark.asyncio
    async def test_broadcast_message(self, websocket_handler):
        """测试广播消息"""
        # 添加活跃连接
        mock_ws1 = Mock()
        mock_ws1.send_json = AsyncMock()
        mock_ws2 = Mock()
        mock_ws2.send_json = AsyncMock()
        
        websocket_handler.active_connections = {
            "session1": mock_ws1,
            "session2": mock_ws2
        }
        
        message = {"type": "broadcast", "content": "广播消息"}
        
        await websocket_handler.broadcast_message(message)
        
        # 验证所有连接都收到消息
        mock_ws1.send_json.assert_called_once_with(message)
        mock_ws2.send_json.assert_called_once_with(message)
    
    @pytest.mark.asyncio
    async def test_broadcast_message_target_sessions(self, websocket_handler):
        """测试向指定会话广播消息"""
        # 添加活跃连接
        mock_ws1 = Mock()
        mock_ws1.send_json = AsyncMock()
        mock_ws2 = Mock()
        mock_ws2.send_json = AsyncMock()
        
        websocket_handler.active_connections = {
            "session1": mock_ws1,
            "session2": mock_ws2
        }
        
        message = {"type": "targeted_broadcast", "content": "定向消息"}
        target_sessions = ["session1"]
        
        await websocket_handler.broadcast_message(message, target_sessions)
        
        # 验证只有指定会话收到消息
        mock_ws1.send_json.assert_called_once_with(message)
        mock_ws2.send_json.assert_not_called()
    
    def test_get_active_connections(self, websocket_handler):
        """测试获取活跃连接"""
        websocket_handler.active_connections = {
            "session1": Mock(),
            "session2": Mock()
        }
        
        connections = websocket_handler.get_active_connections()
        
        assert len(connections) == 2
        assert "session1" in connections
        assert "session2" in connections
        assert connections["session1"] == "connected"
