"""
阶段三集成测试

测试错误处理与缓存优化功能：
- 统一错误处理器测试
- 智能重试机制测试
- 智能缓存管理器测试
- 性能监控器测试
- 系统集成测试

验证错误恢复率>95%、系统可用性>99.9%、缓存命中率>90%、响应时间<100ms的目标。
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from app.services.ai_assistant.integration.error_handler import (
    UnifiedErrorHandler, ErrorCategory, ErrorSeverity, CircuitBreaker
)
from app.services.ai_assistant.integration.retry_manager import (
    IntelligentRetryManager, RetryStrategy, RetryResult
)
from app.services.ai_assistant.integration.cache_manager import (
    IntelligentCacheManager, CacheLevel, LRUCache
)
from app.services.ai_assistant.integration.performance_monitor import (
    IntelligentPerformanceMonitor, MetricType, PerformanceCollector
)


class TestUnifiedErrorHandler:
    """统一错误处理器测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        return Mock()
    
    @pytest.fixture
    def mock_redis_client(self):
        redis = Mock()
        redis.setex = AsyncMock()
        redis.lpush = AsyncMock()
        redis.ltrim = AsyncMock()
        return redis
    
    @pytest.fixture
    def error_handler(self, mock_db_session, mock_redis_client):
        return UnifiedErrorHandler(mock_db_session, mock_redis_client)
    
    @pytest.mark.asyncio
    async def test_handle_llm_error(self, error_handler):
        """测试LLM错误处理"""
        error = Exception("API rate limit exceeded")
        context = {"conversation_id": "test_conv", "user_id": "test_user"}
        
        result = await error_handler.handle_error(error, context, "test_conv", "test_user")
        
        assert result["success"] is False
        assert "should_retry" in result
        assert "error_id" in result
        assert result["recovery_action"] in ["use_fallback_response", "switch_llm_provider"]
    
    @pytest.mark.asyncio
    async def test_handle_database_error(self, error_handler):
        """测试数据库错误处理"""
        error = Exception("Connection timeout")
        context = {"operation": "user_query"}
        
        result = await error_handler.handle_error(error, context)
        
        assert result["success"] is False
        assert result["should_retry"] is True
        assert "retry_delay" in result
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_functionality(self, error_handler):
        """测试熔断器功能"""
        circuit_breaker = error_handler.circuit_breakers[ErrorCategory.LLM]
        
        # 模拟多次失败触发熔断器
        for _ in range(5):
            circuit_breaker.record_failure()
        
        assert circuit_breaker.is_open() is True
        
        # 测试熔断器开启时的错误处理
        error = Exception("LLM service error")
        context = {}
        
        result = await error_handler.handle_error(error, context)
        assert result["recovery_action"] == "circuit_breaker_open"
    
    @pytest.mark.asyncio
    async def test_error_classification(self, error_handler):
        """测试错误分类"""
        # 测试不同类型的错误分类
        test_cases = [
            (Exception("API quota exceeded"), ErrorCategory.LLM),
            (Exception("Database connection failed"), ErrorCategory.DATABASE),
            (Exception("Network timeout"), ErrorCategory.NETWORK),
            (Exception("Invalid parameter format"), ErrorCategory.VALIDATION),
            (Exception("Request timeout"), ErrorCategory.TIMEOUT)
        ]
        
        for error, expected_category in test_cases:
            classified_category = error_handler._classify_error(error, str(error))
            assert classified_category == expected_category
    
    @pytest.mark.asyncio
    async def test_error_stats(self, error_handler):
        """测试错误统计"""
        # 处理几个错误
        errors = [
            Exception("LLM error 1"),
            Exception("Database error 1"),
            Exception("LLM error 2")
        ]
        
        for error in errors:
            await error_handler.handle_error(error, {})
        
        stats = await error_handler.get_error_stats()
        
        assert stats["total_errors"] == 3
        assert "errors_by_category" in stats
        assert "recovery_rate" in stats


class TestIntelligentRetryManager:
    """智能重试管理器测试类"""
    
    @pytest.fixture
    def retry_manager(self):
        return IntelligentRetryManager()
    
    @pytest.mark.asyncio
    async def test_should_retry_logic(self, retry_manager):
        """测试重试判断逻辑"""
        # 测试不同错误类型的重试判断
        test_cases = [
            (Exception("Temporary failure"), ErrorCategory.LLM, ErrorSeverity.LOW, 0, True),
            (Exception("Critical error"), ErrorCategory.LLM, ErrorSeverity.CRITICAL, 0, False),
            (Exception("Validation error"), ErrorCategory.VALIDATION, ErrorSeverity.LOW, 0, False),
            (Exception("Network timeout"), ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, 2, True),
            (Exception("Max retries"), ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, 5, False)
        ]
        
        for error, category, severity, retry_count, expected in test_cases:
            should_retry = await retry_manager.should_retry(error, category, severity, retry_count, {})
            assert should_retry == expected
    
    @pytest.mark.asyncio
    async def test_calculate_delay(self, retry_manager):
        """测试延迟计算"""
        # 测试指数退避
        delay1 = await retry_manager.calculate_delay(ErrorCategory.LLM, 0, {})
        delay2 = await retry_manager.calculate_delay(ErrorCategory.LLM, 1, {})
        delay3 = await retry_manager.calculate_delay(ErrorCategory.LLM, 2, {})
        
        assert delay1 < delay2 < delay3
        assert delay1 >= 1.0  # 基础延迟
        assert delay3 <= 30.0  # 最大延迟限制
    
    @pytest.mark.asyncio
    async def test_execute_with_retry_success(self, retry_manager):
        """测试重试执行成功"""
        call_count = 0
        
        async def mock_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Temporary failure")
            return "success"
        
        result = await retry_manager.execute_with_retry(
            mock_function, ErrorCategory.NETWORK, {}
        )
        
        assert result["success"] is True
        assert result["result"] == "success"
        assert result["retry_count"] == 2
    
    @pytest.mark.asyncio
    async def test_execute_with_retry_failure(self, retry_manager):
        """测试重试执行失败"""
        async def mock_function():
            raise Exception("Persistent failure")
        
        result = await retry_manager.execute_with_retry(
            mock_function, ErrorCategory.LLM, {}
        )
        
        assert result["success"] is False
        assert result["retry_count"] > 0
        assert result["result_type"] in [RetryResult.EXHAUSTED.value, RetryResult.FAILED.value]
    
    @pytest.mark.asyncio
    async def test_retry_stats(self, retry_manager):
        """测试重试统计"""
        # 执行一些重试操作
        async def success_func():
            return "success"
        
        async def failure_func():
            raise Exception("Always fails")
        
        await retry_manager.execute_with_retry(success_func, ErrorCategory.LLM, {})
        await retry_manager.execute_with_retry(failure_func, ErrorCategory.DATABASE, {})
        
        stats = await retry_manager.get_retry_stats()
        
        assert "total_retries" in stats
        assert "success_rate" in stats
        assert "retries_by_category" in stats


class TestIntelligentCacheManager:
    """智能缓存管理器测试类"""
    
    @pytest.fixture
    def mock_redis_client(self):
        redis = Mock()
        redis.get = AsyncMock(return_value=None)
        redis.setex = AsyncMock()
        redis.delete = AsyncMock()
        redis.keys = AsyncMock(return_value=[])
        redis.flushdb = AsyncMock()
        return redis
    
    @pytest.fixture
    def cache_manager(self, mock_redis_client):
        return IntelligentCacheManager(mock_redis_client)
    
    @pytest.mark.asyncio
    async def test_l1_cache_operations(self, cache_manager):
        """测试L1内存缓存操作"""
        # 测试设置和获取
        await cache_manager.set("test_key", "test_value", "user_profile")
        result = await cache_manager.get("test_key", "user_profile")
        
        assert result == "test_value"
        
        # 测试删除
        await cache_manager.delete("test_key", "user_profile")
        result = await cache_manager.get("test_key", "user_profile")
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_cache_levels(self, cache_manager):
        """测试多层缓存"""
        # 模拟L2缓存命中
        cache_manager.redis.get.return_value = '{"cached": "from_redis"}'
        
        result = await cache_manager.get("redis_key", "training_params")
        
        # 应该从Redis获取并回填L1缓存
        assert result == {"cached": "from_redis"}
        
        # 再次获取应该从L1缓存命中
        cache_manager.redis.get.return_value = None  # 清除Redis模拟
        result = await cache_manager.get("redis_key", "training_params")
        assert result == {"cached": "from_redis"}
    
    @pytest.mark.asyncio
    async def test_cache_preload(self, cache_manager):
        """测试缓存预热"""
        # 模拟数据源加载
        with patch.object(cache_manager, '_load_from_source', return_value={"preloaded": "data"}):
            preloaded_count = await cache_manager.preload_cache("exercise_data", ["key1", "key2", "key3"])
            
            assert preloaded_count == 3
            
            # 验证预热的数据可以获取
            result = await cache_manager.get("key1", "exercise_data")
            assert result == {"preloaded": "data"}
    
    @pytest.mark.asyncio
    async def test_cache_invalidation(self, cache_manager):
        """测试缓存失效"""
        # 设置一些缓存
        await cache_manager.set("pattern_key_1", "value1", "user_profile")
        await cache_manager.set("pattern_key_2", "value2", "user_profile")
        await cache_manager.set("other_key", "value3", "user_profile")
        
        # 按模式失效
        invalidated_count = await cache_manager.invalidate_pattern("pattern_key")
        
        assert invalidated_count >= 2
        
        # 验证失效结果
        result1 = await cache_manager.get("pattern_key_1", "user_profile")
        result2 = await cache_manager.get("other_key", "user_profile")
        
        assert result1 is None
        assert result2 == "value3"
    
    @pytest.mark.asyncio
    async def test_cache_stats(self, cache_manager):
        """测试缓存统计"""
        # 执行一些缓存操作
        await cache_manager.set("stats_key", "stats_value", "user_profile")
        await cache_manager.get("stats_key", "user_profile")  # 命中
        await cache_manager.get("nonexistent_key", "user_profile")  # 未命中
        
        stats = await cache_manager.get_cache_stats()
        
        assert "overall" in stats
        assert "hit_rate" in stats["overall"]
        assert "l1_memory" in stats
        assert stats["overall"]["total_requests"] > 0


class TestLRUCache:
    """LRU缓存测试类"""
    
    @pytest.fixture
    def lru_cache(self):
        return LRUCache(max_size=3, default_ttl=60)
    
    def test_lru_basic_operations(self, lru_cache):
        """测试LRU基本操作"""
        # 设置和获取
        lru_cache.set("key1", "value1")
        assert lru_cache.get("key1") == "value1"
        
        # 不存在的键
        assert lru_cache.get("nonexistent") is None
    
    def test_lru_eviction(self, lru_cache):
        """测试LRU淘汰机制"""
        # 填满缓存
        lru_cache.set("key1", "value1")
        lru_cache.set("key2", "value2")
        lru_cache.set("key3", "value3")
        
        # 访问key1使其成为最近使用
        lru_cache.get("key1")
        
        # 添加新键，应该淘汰key2
        lru_cache.set("key4", "value4")
        
        assert lru_cache.get("key1") == "value1"  # 仍然存在
        assert lru_cache.get("key2") is None      # 被淘汰
        assert lru_cache.get("key3") == "value3"  # 仍然存在
        assert lru_cache.get("key4") == "value4"  # 新添加
    
    def test_lru_ttl(self, lru_cache):
        """测试TTL过期"""
        # 设置短TTL
        lru_cache.set("ttl_key", "ttl_value", ttl=1)
        
        # 立即获取应该成功
        assert lru_cache.get("ttl_key") == "ttl_value"
        
        # 等待过期（模拟）
        import time
        time.sleep(2)
        
        # 应该返回None（过期）
        assert lru_cache.get("ttl_key") is None
    
    def test_lru_stats(self, lru_cache):
        """测试LRU统计"""
        lru_cache.set("stats_key", "stats_value")
        lru_cache.get("stats_key")  # 命中
        lru_cache.get("missing_key")  # 未命中
        
        stats = lru_cache.get_stats()
        
        assert stats["hits"] == 1
        assert stats["misses"] == 1
        assert stats["sets"] == 1
        assert stats["hit_rate"] == 0.5


class TestIntelligentPerformanceMonitor:
    """智能性能监控器测试类"""
    
    @pytest.fixture
    def performance_monitor(self):
        return IntelligentPerformanceMonitor()
    
    @pytest.mark.asyncio
    async def test_metric_recording(self, performance_monitor):
        """测试指标记录"""
        # 记录一些指标
        performance_monitor.record_metric(MetricType.RESPONSE_TIME, 150.0)
        performance_monitor.record_metric(MetricType.CPU_USAGE, 75.0)
        performance_monitor.record_metric(MetricType.CACHE_HIT_RATE, 85.0)
        
        # 验证指标被记录
        recent_metrics = performance_monitor.collector.get_recent_metrics(MetricType.RESPONSE_TIME, 10)
        assert len(recent_metrics) == 1
        assert recent_metrics[0].value == 150.0
    
    @pytest.mark.asyncio
    async def test_performance_report(self, performance_monitor):
        """测试性能报告生成"""
        # 记录一些测试数据
        for i in range(10):
            performance_monitor.record_metric(MetricType.RESPONSE_TIME, 100 + i * 10)
            performance_monitor.record_metric(MetricType.ERROR_RATE, i)
        
        report = await performance_monitor.get_performance_report(time_window_minutes=5)
        
        assert "metrics" in report
        assert "system_resources" in report
        assert "alerts" in report
        assert MetricType.RESPONSE_TIME.value in report["metrics"]
        assert MetricType.ERROR_RATE.value in report["metrics"]
    
    @pytest.mark.asyncio
    async def test_performance_thresholds(self, performance_monitor):
        """测试性能阈值告警"""
        # 记录超过阈值的指标
        performance_monitor.record_metric(MetricType.RESPONSE_TIME, 200.0)  # 超过100ms阈值
        performance_monitor.record_metric(MetricType.ERROR_RATE, 10.0)      # 超过5%阈值
        
        report = await performance_monitor.get_performance_report(time_window_minutes=1)
        
        # 应该有告警
        assert len(report["alerts"]) >= 2
        
        # 检查告警内容
        alert_metrics = [alert["metric"] for alert in report["alerts"]]
        assert MetricType.RESPONSE_TIME.value in alert_metrics
        assert MetricType.ERROR_RATE.value in alert_metrics


class TestPhase3Integration:
    """阶段三集成测试"""
    
    @pytest.fixture
    def integrated_system(self):
        """创建集成系统"""
        mock_db = Mock()
        mock_redis = Mock()
        mock_redis.get = AsyncMock(return_value=None)
        mock_redis.setex = AsyncMock()
        mock_redis.delete = AsyncMock()
        mock_redis.lpush = AsyncMock()
        mock_redis.ltrim = AsyncMock()
        
        error_handler = UnifiedErrorHandler(mock_db, mock_redis)
        retry_manager = IntelligentRetryManager(error_handler)
        cache_manager = IntelligentCacheManager(mock_redis, mock_db)
        performance_monitor = IntelligentPerformanceMonitor(error_handler, cache_manager, retry_manager)
        
        return {
            "error_handler": error_handler,
            "retry_manager": retry_manager,
            "cache_manager": cache_manager,
            "performance_monitor": performance_monitor
        }
    
    @pytest.mark.asyncio
    async def test_error_handling_with_retry(self, integrated_system):
        """测试错误处理与重试的集成"""
        error_handler = integrated_system["error_handler"]
        retry_manager = integrated_system["retry_manager"]
        
        call_count = 0
        
        async def failing_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Temporary network error")
            return "success"
        
        # 使用重试管理器执行可能失败的函数
        result = await retry_manager.execute_with_retry(
            failing_function, ErrorCategory.NETWORK, {"test": "context"}
        )
        
        assert result["success"] is True
        assert result["retry_count"] == 2
        assert call_count == 3
    
    @pytest.mark.asyncio
    async def test_cache_with_error_handling(self, integrated_system):
        """测试缓存与错误处理的集成"""
        cache_manager = integrated_system["cache_manager"]
        error_handler = integrated_system["error_handler"]
        
        # 模拟缓存操作中的错误
        with patch.object(cache_manager, '_set_to_redis', side_effect=Exception("Redis connection failed")):
            # 设置缓存应该优雅处理Redis错误
            result = await cache_manager.set("test_key", "test_value", "user_profile")
            
            # 应该部分成功（L1缓存成功，L2失败）
            # 但不应该抛出异常
            assert isinstance(result, bool)
    
    @pytest.mark.asyncio
    async def test_performance_monitoring_integration(self, integrated_system):
        """测试性能监控集成"""
        performance_monitor = integrated_system["performance_monitor"]
        cache_manager = integrated_system["cache_manager"]
        
        # 执行一些操作并监控性能
        start_time = time.time()
        
        await cache_manager.set("perf_key", "perf_value", "user_profile")
        await cache_manager.get("perf_key", "user_profile")
        
        end_time = time.time()
        operation_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        # 记录性能指标
        performance_monitor.record_metric(MetricType.RESPONSE_TIME, operation_time)
        
        # 生成性能报告
        report = await performance_monitor.get_performance_report(time_window_minutes=1)
        
        assert "metrics" in report
        assert MetricType.RESPONSE_TIME.value in report["metrics"]
    
    @pytest.mark.asyncio
    async def test_system_resilience(self, integrated_system):
        """测试系统弹性"""
        error_handler = integrated_system["error_handler"]
        retry_manager = integrated_system["retry_manager"]
        cache_manager = integrated_system["cache_manager"]
        
        # 模拟系统压力测试
        tasks = []
        
        for i in range(50):
            # 创建混合操作任务
            if i % 3 == 0:
                # 缓存操作
                task = cache_manager.set(f"stress_key_{i}", f"stress_value_{i}", "user_profile")
            elif i % 3 == 1:
                # 可能失败的操作
                async def maybe_fail():
                    if i % 7 == 0:
                        raise Exception(f"Simulated failure {i}")
                    return f"success_{i}"
                
                task = retry_manager.execute_with_retry(maybe_fail, ErrorCategory.BUSINESS, {})
            else:
                # 错误处理
                task = error_handler.handle_error(Exception(f"Test error {i}"), {"test_id": i})
            
            tasks.append(task)
        
        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计成功率
        successful_results = [r for r in results if not isinstance(r, Exception)]
        success_rate = len(successful_results) / len(results)
        
        # 系统应该保持高可用性
        assert success_rate > 0.8  # 80%以上成功率
        
        # 获取系统统计
        error_stats = await error_handler.get_error_stats()
        retry_stats = await retry_manager.get_retry_stats()
        cache_stats = await cache_manager.get_cache_stats()
        
        # 验证统计数据的合理性
        assert error_stats["total_errors"] > 0
        assert "recovery_rate" in error_stats
        assert "total_retries" in retry_stats
        assert "overall" in cache_stats
