"""
统一状态适配器测试

测试三套系统间的状态转换功能：
- LangGraph ConversationState
- 原始系统状态字典
- 统一 UnifiedFitnessState

验证状态转换准确率100%的目标。
"""

import pytest
import asyncio
from datetime import datetime

# 使用现有的消息类型
from app.services.state_definitions import AnyMessage, ConversationState

from app.services.ai_assistant.integration.state_adapter import IntegratedStateAdapter
from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState


class TestIntegratedStateAdapter:
    """统一状态适配器测试类"""
    
    @pytest.fixture
    def state_adapter(self):
        """创建状态适配器实例"""
        return IntegratedStateAdapter()
    
    @pytest.fixture
    def sample_conversation_state(self):
        """示例对话状态"""
        return ConversationState(
            conversation_id="test_conv_001",
            user_id="test_user_001",
            session_id="test_session_001",
            intent="training_plan",
            confidence=0.95,
            intent_parameters={"body_part": "chest"},
            user_info={"age": 25, "gender": "male", "height": 175, "weight": 70},
            training_params={"goal": "muscle_gain", "duration": 60},
            meta_info={"stage": "parameter_collection"},
            current_state="collecting_params",
            messages=[
                {"role": "user", "content": "我想练胸肌"},
                {"role": "assistant", "content": "好的，我来为您制定胸肌训练计划"}
            ],
            response_content="正在为您制定训练计划..."
        )
    
    @pytest.fixture
    def sample_dict_state(self):
        """示例字典状态"""
        return {
            "conversation_id": "test_conv_002",
            "user_id": "test_user_002",
            "session_id": "test_session_002",
            "intent": "exercise_recommendation",
            "confidence": 0.88,
            "intent_parameters": {"body_part": "back"},
            "user_profile": {"age": 30, "gender": "female", "fitness_level": "intermediate"},
            "training_params": {"scenario": "gym", "equipment": ["barbell", "dumbbell"]},
            "flow_state": {"stage": "recommendation"},
            "current_state_name": "recommending",
            "messages": [AnyMessage(role="user", content="推荐一些背部训练动作")],
            "response_content": "为您推荐以下背部训练动作..."
        }
    
    @pytest.fixture
    def sample_unified_state(self):
        """示例统一状态"""
        return UnifiedFitnessState(
            conversation_id="test_conv_003",
            user_id="test_user_003",
            session_id="test_session_003",
            timestamp=datetime.now(),
            
            intent="fitness_qa",
            confidence=0.92,
            intent_parameters={"topic": "nutrition"},
            
            user_profile={"age": 28, "gender": "male", "experience": "beginner"},
            training_params={"goal": "weight_loss"},
            fitness_goals=["lose_weight", "build_endurance"],
            
            flow_state={"stage": "answering"},
            current_state_name="qa_processing",
            current_node="fitness_qa_expert",
            processing_system="unified",
            
            response_content="关于营养的问题...",
            response_type="text",
            structured_data={"tips": ["多吃蛋白质", "控制碳水化合物"]},
            
            error_count=0,
            retry_count=0,
            processing_start_time=1234567890.0,
            node_execution_times={"qa_expert": 0.5},
            
            parallel_results=[],
            selected_result=None,
            
            messages=[
                AnyMessage(role="user", content="如何合理安排饮食？"),
                AnyMessage(role="assistant", content="合理的饮食安排需要...")
            ]
        )
    
    @pytest.mark.asyncio
    async def test_conversation_state_to_unified(self, state_adapter, sample_conversation_state):
        """测试对话状态转换为统一状态"""
        # 执行转换
        unified_state = await state_adapter.convert_to_unified(
            sample_conversation_state, "conversation_state", "test_conv_001"
        )
        
        # 验证基础字段
        assert unified_state["conversation_id"] == "test_conv_001"
        assert unified_state["user_id"] == "test_user_001"
        assert unified_state["session_id"] == "test_session_001"
        assert unified_state["intent"] == "training_plan"
        assert unified_state["confidence"] == 0.95
        assert unified_state["intent_parameters"]["body_part"] == "chest"
        
        # 验证用户信息
        assert unified_state["user_profile"]["age"] == 25
        assert unified_state["user_profile"]["gender"] == "male"
        
        # 验证训练参数
        assert unified_state["training_params"]["goal"] == "muscle_gain"
        assert unified_state["training_params"]["duration"] == 60
        
        # 验证状态信息
        assert unified_state["current_state_name"] == "collecting_params"
        assert unified_state["processing_system"] == "conversation_state"
        
        # 验证消息转换
        assert len(unified_state["messages"]) == 2
        assert isinstance(unified_state["messages"][0], AnyMessage)
        assert unified_state["messages"][0].content == "我想练胸肌"
    
    @pytest.mark.asyncio
    async def test_dict_state_to_unified(self, state_adapter, sample_dict_state):
        """测试字典状态转换为统一状态"""
        # 执行转换
        unified_state = await state_adapter.convert_to_unified(
            sample_dict_state, "dict", "test_conv_002"
        )
        
        # 验证转换结果
        assert unified_state["conversation_id"] == "test_conv_002"
        assert unified_state["intent"] == "exercise_recommendation"
        assert unified_state["confidence"] == 0.88
        assert unified_state["user_profile"]["fitness_level"] == "intermediate"
        assert unified_state["training_params"]["scenario"] == "gym"
        assert unified_state["processing_system"] == "dict"
    
    @pytest.mark.asyncio
    async def test_unified_to_conversation_state(self, state_adapter, sample_unified_state):
        """测试统一状态转换为对话状态"""
        # 执行转换
        conversation_state = await state_adapter.convert_from_unified(
            sample_unified_state, "conversation_state"
        )
        
        # 验证转换结果
        assert conversation_state["conversation_id"] == "test_conv_003"
        assert conversation_state["intent"] == "fitness_qa"
        assert conversation_state["confidence"] == 0.92
        assert conversation_state["user_info"]["age"] == 28
        assert conversation_state["training_params"]["goal"] == "weight_loss"
    
    @pytest.mark.asyncio
    async def test_unified_to_dict_state(self, state_adapter, sample_unified_state):
        """测试统一状态转换为字典状态"""
        # 执行转换
        dict_state = await state_adapter.convert_from_unified(
            sample_unified_state, "dict"
        )
        
        # 验证转换结果
        assert isinstance(dict_state, dict)
        assert dict_state["conversation_id"] == "test_conv_003"
        assert dict_state["intent"] == "fitness_qa"
        assert dict_state["user_profile"]["age"] == 28
    
    @pytest.mark.asyncio
    async def test_state_validation(self, state_adapter, sample_unified_state):
        """测试状态验证功能"""
        # 验证完整状态
        is_valid = await state_adapter.validate_state(sample_unified_state)
        assert is_valid is True
        
        # 验证不完整状态
        incomplete_state = UnifiedFitnessState(
            conversation_id="test",
            user_id="test"
            # 缺少其他必需字段
        )
        
        with pytest.raises(ValueError):
            await state_adapter.validate_state(incomplete_state)
    
    @pytest.mark.asyncio
    async def test_message_conversion(self, state_adapter):
        """测试消息格式转换"""
        # 测试不同格式的消息
        messages = [
            AnyMessage(role="user", content="Hello"),
            AnyMessage(role="assistant", content="Hi there"),
            {"role": "user", "content": "How are you?"},
            {"role": "assistant", "content": "I'm fine"},
            "Simple string message"
        ]

        converted = state_adapter._convert_messages(messages)

        # 验证转换结果
        assert len(converted) == 5
        assert all(isinstance(msg, AnyMessage) for msg in converted)
        assert converted[0].content == "Hello"
        assert converted[2].content == "How are you?"
        assert converted[4].content == "Simple string message"
    
    @pytest.mark.asyncio
    async def test_caching_mechanism(self, state_adapter, sample_dict_state):
        """测试缓存机制"""
        # 第一次转换
        start_time = asyncio.get_event_loop().time()
        unified_state1 = await state_adapter.convert_to_unified(
            sample_dict_state, "dict", "test_conv_cache"
        )
        first_duration = asyncio.get_event_loop().time() - start_time
        
        # 第二次转换（应该从缓存获取）
        start_time = asyncio.get_event_loop().time()
        unified_state2 = await state_adapter.convert_to_unified(
            sample_dict_state, "dict", "test_conv_cache"
        )
        second_duration = asyncio.get_event_loop().time() - start_time
        
        # 验证缓存效果
        assert unified_state1 == unified_state2
        assert second_duration < first_duration  # 第二次应该更快
    
    @pytest.mark.asyncio
    async def test_error_handling(self, state_adapter):
        """测试错误处理"""
        # 测试无效的源类型
        unified_state = await state_adapter.convert_to_unified(
            {}, "invalid_type", "test_conv_error"
        )
        
        # 应该返回默认状态
        assert unified_state["conversation_id"] == "test_conv_error"
        assert unified_state["processing_system"] == "default"
        assert unified_state["error_count"] == 0
    
    @pytest.mark.asyncio
    async def test_performance_benchmark(self, state_adapter, sample_dict_state):
        """性能基准测试"""
        # 测试100次转换的平均时间
        total_time = 0
        iterations = 100
        
        for i in range(iterations):
            start_time = asyncio.get_event_loop().time()
            await state_adapter.convert_to_unified(
                sample_dict_state, "dict", f"test_conv_{i}"
            )
            total_time += asyncio.get_event_loop().time() - start_time
        
        average_time = total_time / iterations
        
        # 验证平均响应时间 < 10ms
        assert average_time < 0.01, f"平均转换时间过长: {average_time:.4f}s"
    
    @pytest.mark.asyncio
    async def test_concurrent_conversion(self, state_adapter, sample_dict_state):
        """并发转换测试"""
        # 创建多个并发转换任务
        tasks = []
        for i in range(50):
            task = state_adapter.convert_to_unified(
                sample_dict_state, "dict", f"concurrent_test_{i}"
            )
            tasks.append(task)
        
        # 执行并发转换
        results = await asyncio.gather(*tasks)
        
        # 验证所有转换都成功
        assert len(results) == 50
        assert all(result["conversation_id"].startswith("concurrent_test_") for result in results)
        assert all(result["intent"] == "exercise_recommendation" for result in results)
