"""
整合状态管理器测试

测试多源状态获取和保存功能：
- LangGraph检查点存储
- 原始系统缓存
- 数据库持久化存储

验证状态管理一致性100%的目标。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from app.services.ai_assistant.integration.state_manager import IntegratedStateManager
from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState


class TestIntegratedStateManager:
    """整合状态管理器测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        return Mock()
    
    @pytest.fixture
    def mock_redis_client(self):
        """模拟Redis客户端"""
        redis_mock = Mock()
        redis_mock.get.return_value = None
        redis_mock.setex.return_value = True
        redis_mock.delete.return_value = True
        return redis_mock
    
    @pytest.fixture
    def state_manager(self, mock_db_session, mock_redis_client):
        """创建状态管理器实例"""
        with patch('app.services.ai_assistant.integration.state_manager.PostgreSQLCheckpointer'):
            manager = IntegratedStateManager(mock_db_session, mock_redis_client)
            # 模拟检查点存储器
            manager.checkpointer = Mock()
            manager.checkpointer.aget_checkpoint = AsyncMock(return_value=None)
            manager.checkpointer.aput_checkpoint = AsyncMock(return_value=True)
            manager.checkpointer.adelete_checkpoint = AsyncMock(return_value=True)
            return manager
    
    @pytest.fixture
    def sample_unified_state(self):
        """示例统一状态"""
        return UnifiedFitnessState(
            conversation_id="test_conv_001",
            user_id="test_user_001",
            session_id="test_session_001",
            timestamp=datetime.now(),
            
            intent="training_plan",
            confidence=0.95,
            intent_parameters={"body_part": "chest"},
            
            user_profile={"age": 25, "gender": "male"},
            training_params={"goal": "muscle_gain", "duration": 60},
            fitness_goals=["build_muscle"],
            
            flow_state={"stage": "planning"},
            current_state_name="planning",
            current_node="training_plan_expert",
            processing_system="unified",
            
            response_content="正在制定训练计划...",
            response_type="text",
            structured_data={},
            
            error_count=0,
            retry_count=0,
            processing_start_time=1234567890.0,
            node_execution_times={},
            
            parallel_results=[],
            selected_result=None,
            
            messages=[]
        )
    
    @pytest.mark.asyncio
    async def test_get_current_state_from_cache(self, state_manager, sample_unified_state):
        """测试从缓存获取状态"""
        # 模拟缓存中有数据
        state_manager.memory_cache_service.get_session_state = Mock(return_value=sample_unified_state)
        
        # 获取状态
        result = await state_manager.get_current_state("test_conv_001")
        
        # 验证结果
        assert result["conversation_id"] == "test_conv_001"
        assert result["intent"] == "training_plan"
        assert result["confidence"] == 0.95
    
    @pytest.mark.asyncio
    async def test_get_current_state_from_checkpoint(self, state_manager, sample_unified_state):
        """测试从LangGraph检查点获取状态"""
        # 模拟缓存中没有数据，但检查点中有
        state_manager.memory_cache_service.get_session_state = Mock(return_value=None)
        state_manager._get_from_langgraph_checkpoint = AsyncMock(return_value=sample_unified_state)
        state_manager._cache_state = AsyncMock(return_value=True)
        
        # 获取状态
        result = await state_manager.get_current_state("test_conv_001")
        
        # 验证结果
        assert result["conversation_id"] == "test_conv_001"
        assert result["intent"] == "training_plan"
        
        # 验证缓存被调用
        state_manager._cache_state.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_current_state_from_original_system(self, state_manager, sample_unified_state):
        """测试从原始系统获取状态"""
        # 模拟缓存和检查点都没有数据，但原始系统有
        state_manager.memory_cache_service.get_session_state = Mock(return_value=None)
        state_manager._get_from_langgraph_checkpoint = AsyncMock(return_value=None)
        state_manager._get_from_original_system = AsyncMock(return_value=sample_unified_state)
        state_manager._cache_state = AsyncMock(return_value=True)
        
        # 获取状态
        result = await state_manager.get_current_state("test_conv_001")
        
        # 验证结果
        assert result["conversation_id"] == "test_conv_001"
        assert result["intent"] == "training_plan"
        
        # 验证缓存被调用
        state_manager._cache_state.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_current_state_create_new(self, state_manager):
        """测试创建新状态"""
        # 模拟所有存储都没有数据
        state_manager.memory_cache_service.get_session_state = Mock(return_value=None)
        state_manager._get_from_langgraph_checkpoint = AsyncMock(return_value=None)
        state_manager._get_from_original_system = AsyncMock(return_value=None)
        state_manager._cache_state = AsyncMock(return_value=True)
        
        # 获取状态
        result = await state_manager.get_current_state("test_conv_new")
        
        # 验证结果
        assert result["conversation_id"] == "test_conv_new"
        assert result["current_state_name"] == "idle"
        assert result["processing_system"] == "default"
        
        # 验证缓存被调用
        state_manager._cache_state.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_save_state_success(self, state_manager, sample_unified_state):
        """测试状态保存成功"""
        # 模拟所有保存操作都成功
        state_manager._save_to_langgraph_checkpoint = AsyncMock(return_value=True)
        state_manager._save_to_original_system = AsyncMock(return_value=True)
        state_manager._cache_state = AsyncMock(return_value=True)
        
        # 保存状态
        result = await state_manager.save_state(sample_unified_state)
        
        # 验证结果
        assert result is True
        
        # 验证所有保存方法都被调用
        state_manager._save_to_langgraph_checkpoint.assert_called_once()
        state_manager._save_to_original_system.assert_called_once()
        state_manager._cache_state.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_save_state_partial_success(self, state_manager, sample_unified_state):
        """测试状态部分保存成功"""
        # 模拟部分保存操作成功
        state_manager._save_to_langgraph_checkpoint = AsyncMock(side_effect=Exception("Checkpoint failed"))
        state_manager._save_to_original_system = AsyncMock(return_value=True)
        state_manager._cache_state = AsyncMock(return_value=True)
        
        # 保存状态
        result = await state_manager.save_state(sample_unified_state)
        
        # 验证结果（至少一个成功就返回True）
        assert result is True
    
    @pytest.mark.asyncio
    async def test_save_state_all_failed(self, state_manager, sample_unified_state):
        """测试状态保存全部失败"""
        # 模拟所有保存操作都失败
        state_manager._save_to_langgraph_checkpoint = AsyncMock(side_effect=Exception("Checkpoint failed"))
        state_manager._save_to_original_system = AsyncMock(side_effect=Exception("Original failed"))
        state_manager._cache_state = AsyncMock(side_effect=Exception("Cache failed"))
        
        # 保存状态
        result = await state_manager.save_state(sample_unified_state)
        
        # 验证结果
        assert result is False
    
    @pytest.mark.asyncio
    async def test_delete_state_success(self, state_manager):
        """测试状态删除成功"""
        # 模拟所有删除操作都成功
        state_manager._delete_langgraph_checkpoint = AsyncMock(return_value=True)
        state_manager._delete_original_system_state = AsyncMock(return_value=True)
        state_manager._delete_cache_state = AsyncMock(return_value=True)
        
        # 删除状态
        result = await state_manager.delete_state("test_conv_001")
        
        # 验证结果
        assert result is True
        
        # 验证所有删除方法都被调用
        state_manager._delete_langgraph_checkpoint.assert_called_once()
        state_manager._delete_original_system_state.assert_called_once()
        state_manager._delete_cache_state.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_migrate_from_legacy(self, state_manager):
        """测试从原始系统迁移状态"""
        # 模拟数据库查询结果
        mock_conversation = Mock()
        mock_conversation.id = 1
        mock_conversation.user_id = 123
        mock_conversation.metadata = {
            "user_info": {"age": 25},
            "training_params": {"goal": "muscle_gain"},
            "intent": "training_plan",
            "confidence": 0.9
        }
        
        mock_messages = [
            Mock(is_user=True, content="我想练胸肌"),
            Mock(is_user=False, content="好的，我来为您制定计划")
        ]
        
        with patch('app.services.ai_assistant.integration.state_manager.crud_conversation') as mock_crud_conv, \
             patch('app.services.ai_assistant.integration.state_manager.crud_message') as mock_crud_msg:
            
            mock_crud_conv.get_by_session_id.return_value = mock_conversation
            mock_crud_msg.get_by_conversation_id.return_value = mock_messages
            
            # 执行迁移
            result = await state_manager.migrate_from_legacy("test_conv_legacy")
            
            # 验证结果
            assert result is not None
            assert result["conversation_id"] == "test_conv_legacy"
            assert result["user_id"] == "123"
            assert result["intent"] == "training_plan"
            assert result["confidence"] == 0.9
    
    @pytest.mark.asyncio
    async def test_caching_mechanism(self, state_manager, sample_unified_state):
        """测试缓存机制"""
        # 测试缓存设置
        result = await state_manager._cache_state("test_conv_cache", sample_unified_state)
        assert result is True
        
        # 验证内存缓存被调用
        state_manager.memory_cache_service.set_session_state.assert_called_once()
        
        # 验证Redis缓存被调用
        state_manager.redis_client.setex.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_performance_get_state(self, state_manager, sample_unified_state):
        """性能测试 - 状态获取"""
        # 模拟缓存命中
        state_manager.memory_cache_service.get_session_state = Mock(return_value=sample_unified_state)
        
        # 测试100次获取的平均时间
        total_time = 0
        iterations = 100
        
        for i in range(iterations):
            start_time = asyncio.get_event_loop().time()
            await state_manager.get_current_state(f"test_conv_{i}")
            total_time += asyncio.get_event_loop().time() - start_time
        
        average_time = total_time / iterations
        
        # 验证平均响应时间 < 100ms
        assert average_time < 0.1, f"平均获取时间过长: {average_time:.4f}s"
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self, state_manager, sample_unified_state):
        """并发操作测试"""
        # 模拟缓存操作
        state_manager.memory_cache_service.get_session_state = Mock(return_value=sample_unified_state)
        state_manager._save_to_langgraph_checkpoint = AsyncMock(return_value=True)
        state_manager._save_to_original_system = AsyncMock(return_value=True)
        state_manager._cache_state = AsyncMock(return_value=True)
        
        # 创建并发任务
        get_tasks = [state_manager.get_current_state(f"concurrent_get_{i}") for i in range(25)]
        save_tasks = [state_manager.save_state(sample_unified_state) for i in range(25)]
        
        # 执行并发操作
        get_results = await asyncio.gather(*get_tasks)
        save_results = await asyncio.gather(*save_tasks)
        
        # 验证结果
        assert len(get_results) == 25
        assert len(save_results) == 25
        assert all(result["conversation_id"].startswith("concurrent_get_") for result in get_results)
        assert all(result is True for result in save_results)
    
    @pytest.mark.asyncio
    async def test_error_recovery(self, state_manager):
        """错误恢复测试"""
        # 模拟所有获取操作都失败
        state_manager.memory_cache_service.get_session_state = Mock(side_effect=Exception("Cache error"))
        state_manager._get_from_langgraph_checkpoint = AsyncMock(side_effect=Exception("Checkpoint error"))
        state_manager._get_from_original_system = AsyncMock(side_effect=Exception("Original error"))
        
        # 获取状态（应该返回紧急状态）
        result = await state_manager.get_current_state("test_conv_error")
        
        # 验证紧急状态
        assert result["conversation_id"] == "test_conv_error"
        assert result["error_count"] == 1
        assert result["response_type"] == "error"
        assert "系统遇到问题" in result["response_content"]
