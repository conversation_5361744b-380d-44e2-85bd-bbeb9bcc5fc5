"""
整合意图处理器测试

测试三层意图处理策略：
- 原始系统的上下文感知意图识别
- LangGraph的智能路由决策
- 统一架构的专家节点处理

验证意图识别准确率>95%的目标。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from langchain_core.messages import HumanMessage, AIMessage

from app.services.ai_assistant.integration.intent_processor import IntegratedIntentProcessor
from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.intent.recognition.recognizer import IntentResult


class TestIntegratedIntentProcessor:
    """整合意图处理器测试类"""
    
    @pytest.fixture
    def mock_llm_service(self):
        """模拟LLM服务"""
        return Mock()
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        return Mock()
    
    @pytest.fixture
    def intent_processor(self, mock_llm_service, mock_db_session):
        """创建意图处理器实例"""
        with patch('app.services.ai_assistant.integration.intent_processor.IntentRecognizer'):
            processor = IntegratedIntentProcessor(mock_llm_service, mock_db_session)
            
            # 模拟原始意图识别器
            processor.original_recognizer = Mock()
            processor.original_recognizer.recognize_intent = AsyncMock()
            
            return processor
    
    @pytest.fixture
    def sample_unified_state(self):
        """示例统一状态"""
        return UnifiedFitnessState(
            conversation_id="test_conv_001",
            user_id="test_user_001",
            session_id="test_session_001",
            timestamp=datetime.now(),
            
            intent="",
            confidence=0.0,
            intent_parameters={},
            
            user_profile={"age": 25, "gender": "male", "fitness_level": "beginner"},
            training_params={"goal": "muscle_gain"},
            fitness_goals=[],
            
            flow_state={"stage": "initial"},
            current_state_name="idle",
            current_node="",
            processing_system="unified",
            
            response_content="",
            response_type="text",
            structured_data={},
            
            error_count=0,
            retry_count=0,
            processing_start_time=0.0,
            node_execution_times={},
            
            parallel_results=[],
            selected_result=None,
            
            messages=[
                HumanMessage(content="我想练胸肌"),
                AIMessage(content="好的，我来为您制定训练计划")
            ]
        )
    
    @pytest.mark.asyncio
    async def test_high_confidence_intent_processing(self, intent_processor, sample_unified_state):
        """测试高置信度意图处理"""
        # 模拟高置信度意图识别结果
        intent_result = IntentResult(
            intent="training_plan",
            confidence=0.95,
            parameters={"body_part": "chest"},
            sub_intents=[],
            recognition_time=0.1
        )
        
        intent_processor.original_recognizer.recognize_intent.return_value = intent_result
        intent_processor._call_expert_node = AsyncMock(return_value={
            "response_content": "为您制定胸肌训练计划...",
            "response_type": "text",
            "current_node": "training_plan_expert"
        })
        
        # 处理意图
        result = await intent_processor.process_intent("我想练胸肌", sample_unified_state)
        
        # 验证结果
        assert result["intent"] == "training_plan"
        assert result["confidence"] == 0.95
        assert result["intent_parameters"]["body_part"] == "chest"
        assert "训练计划" in result["response_content"]
        assert "training_plan" in result["processing_path"][-1]
    
    @pytest.mark.asyncio
    async def test_low_confidence_intelligent_routing(self, intent_processor, sample_unified_state):
        """测试低置信度智能路由"""
        # 模拟低置信度意图识别结果
        intent_result = IntentResult(
            intent="unknown",
            confidence=0.3,
            parameters={},
            sub_intents=[],
            recognition_time=0.1
        )
        
        intent_processor.original_recognizer.recognize_intent.return_value = intent_result
        intent_processor._intelligent_routing_decision = AsyncMock(return_value="exercise_recommendation")
        intent_processor._call_expert_node = AsyncMock(return_value={
            "response_content": "为您推荐运动...",
            "response_type": "text",
            "current_node": "exercise_recommendation_expert"
        })
        
        # 处理意图
        result = await intent_processor.process_intent("推荐一些动作", sample_unified_state)
        
        # 验证结果
        assert result["intent"] == "unknown"
        assert result["confidence"] == 0.3
        assert "推荐运动" in result["response_content"]
        assert "exercise_recommendation" in result["processing_path"][-1]
    
    @pytest.mark.asyncio
    async def test_intent_recognition_standalone(self, intent_processor):
        """测试独立意图识别功能"""
        # 模拟意图识别结果
        intent_result = IntentResult(
            intent="fitness_qa",
            confidence=0.88,
            parameters={"topic": "nutrition"},
            sub_intents=["diet_advice"],
            recognition_time=0.2
        )
        
        intent_processor.original_recognizer.recognize_intent.return_value = intent_result
        
        # 执行意图识别
        context = {
            "conversation_id": "test_conv_001",
            "user_profile": {"age": 25},
            "training_params": {"goal": "weight_loss"},
            "messages": [{"role": "user", "content": "如何合理饮食？"}]
        }
        
        result = await intent_processor.recognize_intent("如何合理饮食？", context)
        
        # 验证结果
        assert result.intent == "fitness_qa"
        assert result.confidence == 0.88
        assert result.parameters["topic"] == "nutrition"
        assert "diet_advice" in result.sub_intents
    
    @pytest.mark.asyncio
    async def test_route_to_handler(self, intent_processor, sample_unified_state):
        """测试路由到处理器"""
        # 模拟专家节点调用
        intent_processor._call_expert_node = AsyncMock(return_value={
            "response_content": "健身问答响应...",
            "response_type": "text",
            "current_node": "fitness_qa_expert"
        })
        
        # 执行路由
        result = await intent_processor.route_to_handler("fitness_qa", sample_unified_state)
        
        # 验证结果
        assert "健身问答响应" in result["response_content"]
        assert result["current_node"] == "fitness_qa_expert"
    
    @pytest.mark.asyncio
    async def test_keyword_based_routing(self, intent_processor):
        """测试基于关键词的路由"""
        # 测试不同关键词的路由结果
        test_cases = [
            ("我想制定训练计划", "training_plan"),
            ("推荐一些胸部动作", "exercise_recommendation"),
            ("如何正确深蹲？", "fitness_qa"),
            ("吃什么有助于增肌？", "diet_advice"),
            ("你好", "general_chat")
        ]
        
        for message, expected_intent in test_cases:
            result = intent_processor._keyword_based_routing(message)
            assert result == expected_intent, f"消息 '{message}' 应该路由到 '{expected_intent}'，实际路由到 '{result}'"
    
    @pytest.mark.asyncio
    async def test_expert_node_calling(self, intent_processor, sample_unified_state):
        """测试专家节点调用"""
        # 模拟专家节点
        mock_expert = AsyncMock(return_value={
            "response_content": "专家节点响应",
            "response_type": "text",
            "structured_data": {"tips": ["tip1", "tip2"]}
        })
        
        intent_processor.expert_nodes["test_expert"] = mock_expert
        
        # 调用专家节点
        result = await intent_processor._call_expert_node("test_expert", sample_unified_state)
        
        # 验证结果
        assert result["response_content"] == "专家节点响应"
        assert result["structured_data"]["tips"] == ["tip1", "tip2"]
        mock_expert.assert_called_once_with(sample_unified_state)
    
    @pytest.mark.asyncio
    async def test_context_extraction(self, intent_processor, sample_unified_state):
        """测试上下文提取"""
        context = intent_processor._extract_context(sample_unified_state)
        
        # 验证上下文内容
        assert context["conversation_id"] == "test_conv_001"
        assert context["user_profile"]["age"] == 25
        assert context["training_params"]["goal"] == "muscle_gain"
        assert len(context["messages"]) == 2
    
    @pytest.mark.asyncio
    async def test_recent_messages_extraction(self, intent_processor):
        """测试最近消息提取"""
        # 创建测试消息
        messages = [
            HumanMessage(content="消息1"),
            AIMessage(content="回复1"),
            {"role": "user", "content": "消息2"},
            {"role": "assistant", "content": "回复2"},
            HumanMessage(content="消息3")
        ]
        
        recent_messages = intent_processor._extract_recent_messages(messages)
        
        # 验证提取结果
        assert len(recent_messages) == 5
        assert recent_messages[0]["role"] == "user"
        assert recent_messages[0]["content"] == "消息1"
        assert recent_messages[1]["role"] == "assistant"
        assert recent_messages[2]["content"] == "消息2"
    
    @pytest.mark.asyncio
    async def test_fallback_processing(self, intent_processor, sample_unified_state):
        """测试降级处理"""
        # 模拟通用聊天专家
        intent_processor._call_expert_node = AsyncMock(return_value={
            "response_content": "抱歉，我没有理解您的意思，请重新描述。",
            "response_type": "text",
            "current_node": "general_chat_expert"
        })
        
        # 执行降级处理
        result = await intent_processor._fallback_intent_processing("无法理解的消息", sample_unified_state)
        
        # 验证结果
        assert result["intent"] == "general_chat"
        assert result["confidence"] == 0.5
        assert result["error_count"] == 1
        assert "fallback" in result["processing_path"][-1]
    
    @pytest.mark.asyncio
    async def test_error_handling(self, intent_processor, sample_unified_state):
        """测试错误处理"""
        # 模拟意图识别失败
        intent_processor.original_recognizer.recognize_intent.side_effect = Exception("Recognition failed")
        intent_processor._call_expert_node = AsyncMock(return_value={
            "response_content": "系统遇到问题...",
            "response_type": "error"
        })
        
        # 处理意图（应该触发降级处理）
        result = await intent_processor.process_intent("测试消息", sample_unified_state)
        
        # 验证错误处理
        assert result["intent"] == "general_chat"
        assert result["error_count"] == 1
    
    @pytest.mark.asyncio
    async def test_performance_intent_processing(self, intent_processor, sample_unified_state):
        """性能测试 - 意图处理"""
        # 模拟快速响应
        intent_result = IntentResult(
            intent="training_plan",
            confidence=0.9,
            parameters={},
            sub_intents=[],
            recognition_time=0.01
        )
        
        intent_processor.original_recognizer.recognize_intent.return_value = intent_result
        intent_processor._call_expert_node = AsyncMock(return_value={
            "response_content": "快速响应",
            "response_type": "text"
        })
        
        # 测试100次处理的平均时间
        total_time = 0
        iterations = 100
        
        for i in range(iterations):
            start_time = asyncio.get_event_loop().time()
            await intent_processor.process_intent(f"测试消息 {i}", sample_unified_state)
            total_time += asyncio.get_event_loop().time() - start_time
        
        average_time = total_time / iterations
        
        # 验证平均处理时间 < 200ms
        assert average_time < 0.2, f"平均处理时间过长: {average_time:.4f}s"
    
    @pytest.mark.asyncio
    async def test_concurrent_intent_processing(self, intent_processor, sample_unified_state):
        """并发意图处理测试"""
        # 模拟意图识别
        intent_result = IntentResult(
            intent="general_chat",
            confidence=0.8,
            parameters={},
            sub_intents=[],
            recognition_time=0.05
        )
        
        intent_processor.original_recognizer.recognize_intent.return_value = intent_result
        intent_processor._call_expert_node = AsyncMock(return_value={
            "response_content": "并发响应",
            "response_type": "text"
        })
        
        # 创建并发任务
        tasks = []
        for i in range(50):
            task = intent_processor.process_intent(f"并发消息 {i}", sample_unified_state)
            tasks.append(task)
        
        # 执行并发处理
        results = await asyncio.gather(*tasks)
        
        # 验证结果
        assert len(results) == 50
        assert all(result["intent"] == "general_chat" for result in results)
        assert all(result["confidence"] == 0.8 for result in results)
        assert all("并发响应" in result["response_content"] for result in results)
    
    @pytest.mark.asyncio
    async def test_intent_accuracy_simulation(self, intent_processor):
        """意图识别准确率模拟测试"""
        # 测试用例：消息 -> 期望意图
        test_cases = [
            ("我想制定一个胸肌训练计划", "training_plan"),
            ("推荐一些背部训练动作", "exercise_recommendation"),
            ("如何正确进行深蹲？", "fitness_qa"),
            ("增肌期间应该吃什么？", "diet_advice"),
            ("你好，今天天气不错", "general_chat"),
            ("我想减肥，有什么建议吗？", "fitness_advice"),
            ("制定一个一周的健身计划", "training_plan"),
            ("哑铃卧推的标准动作是什么？", "fitness_qa"),
            ("推荐一些有氧运动", "exercise_recommendation"),
            ("蛋白质摄入量应该是多少？", "diet_advice")
        ]
        
        correct_predictions = 0
        
        for message, expected_intent in test_cases:
            # 使用关键词路由模拟意图识别
            predicted_intent = intent_processor._keyword_based_routing(message)
            
            if predicted_intent == expected_intent:
                correct_predictions += 1
        
        accuracy = correct_predictions / len(test_cases)
        
        # 验证准确率 > 80% (关键词路由的基准)
        assert accuracy > 0.8, f"意图识别准确率过低: {accuracy:.2%}"
