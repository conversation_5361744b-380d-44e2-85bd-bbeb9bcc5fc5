#!/usr/bin/env python3
"""
测试所有训练模板相关的 API 端点
"""

import sys
import os
sys.path.insert(0, '/home/<USER>/backend')

from app.services.training_template_service import TrainingTemplateService
from app.schemas.training_plan import TrainingTemplateCreate, TrainingTemplateExerciseCreate
from app.db.session import get_db
from app.models.user import User
from app.models.training_template import WorkoutTemplate

def test_all_endpoints():
    """测试所有训练模板端点"""
    
    print("🧪 测试所有训练模板相关的 API 端点")
    
    db = next(get_db())
    try:
        # 获取测试用户
        user = db.query(User).first()
        if not user:
            print("❌ 没有找到测试用户")
            return False
        
        template_service = TrainingTemplateService(db)
        
        # 1. 测试获取用户所有模板
        print("\n1️⃣ 测试获取用户所有模板")
        templates = template_service.get_user_templates_with_details(user.id)
        print(f"✅ 获取到 {len(templates)} 个模板")
        
        if templates:
            template_id = templates[0].id
            print(f"  • 使用模板ID {template_id} 进行后续测试")
            
            # 2. 测试获取特定模板
            print("\n2️⃣ 测试获取特定模板")
            specific_template = template_service.get_template_with_details(template_id, user.id)
            if specific_template:
                template_dict = specific_template.to_dict()
                print(f"✅ 获取特定模板成功")
                print(f"  • 模板名称: {template_dict.get('name')}")
                print(f"  • visibility: {template_dict.get('visibility')}")
                print(f"  • 运动数量: {template_dict.get('exercise_count')}")
            else:
                print("❌ 获取特定模板失败")
                return False
            
            # 3. 测试更新模板
            print("\n3️⃣ 测试更新模板")
            update_data = TrainingTemplateCreate(
                name="更新后的模板名称",
                description="更新后的描述",
                estimated_duration=90,
                target_body_parts=[2, 6, 5],
                training_scenario="gym",
                visibility="public",  # 更新 visibility
                notes="更新后的备注",
                exercises=[
                    TrainingTemplateExerciseCreate(
                        exercise_id=1,
                        sets=4,
                        reps="8-10",
                        weight="25",
                        rest_seconds=90,
                        exercise_type="weight_reps"
                    )
                ]
            )
            
            update_result = template_service.update_template(template_id, update_data, user.id)
            if update_result:
                print(f"✅ 更新模板成功")
                updated_template = update_result.get('template', {})
                print(f"  • 新名称: {updated_template.get('name')}")
                print(f"  • 新visibility: {updated_template.get('visibility')}")
                print(f"  • 更新的字段: {update_result.get('updated_fields', [])}")
            else:
                print("❌ 更新模板失败")
                return False
        
        # 4. 测试创建新模板
        print("\n4️⃣ 测试创建新模板")
        create_data = TrainingTemplateCreate(
            name="端点测试模板",
            description="测试所有端点",
            estimated_duration=45,
            target_body_parts=[1, 3],
            training_scenario="home",
            visibility="private",
            notes="端点测试",
            exercises=[
                TrainingTemplateExerciseCreate(
                    exercise_id=2,
                    sets=3,
                    reps="15",
                    weight="15",
                    rest_seconds=45,
                    exercise_type="weight_reps"
                )
            ]
        )
        
        new_template = template_service.create_template(create_data, user.id)
        if new_template:
            new_template_dict = new_template.to_dict()
            print(f"✅ 创建新模板成功")
            print(f"  • 模板ID: {new_template_dict.get('id')}")
            print(f"  • 模板名称: {new_template_dict.get('name')}")
            print(f"  • visibility: {new_template_dict.get('visibility')}")
        else:
            print("❌ 创建新模板失败")
            return False
        
        print("\n✅ 所有端点测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = test_all_endpoints()
    if success:
        print("\n🎉 所有训练模板 API 端点工作正常！")
    else:
        print("\n❌ 部分端点存在问题")
        sys.exit(1)
